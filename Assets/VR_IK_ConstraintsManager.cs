using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Animations.Rigging;
using Avaturn.Core.Runtime.Scripts.Avatar;


public class VR_IK_ConstraintsManager : MonoBehaviour
{
    public Transform playerCharacterRoot;

    public Rig rig;
    public RigBuilder rigBuilder;
    public RigBuilder rigBuilderPlayer;
    public Animator player<PERSON><PERSON><PERSON><PERSON><PERSON>or, PlayerAnimator;

    [<PERSON><PERSON>("Body IK Constraints")]

    [Toolt<PERSON>("Hands")]
    public TwoBoneIKConstraint rightHandIK;
    public TwoBoneIKConstraint leftHandIK;

    [Tooltip("Head")]
    public MultiParentConstraint headIK;

    [Tooltip("Legs")]
    public TwoBoneIKConstraint rightLegIK;
    public TwoBoneIKConstraint leftLegIK;

    public RuntimeAnimatorController playerAimationClip;


    [Header("Body Part Name")]

    [Tooltip("Hand Name")]
    public string rightHandTipName;
    public string rightHandMidName;
    public string rightHandRootName;


    public string leftHandTipName;
    public string leftHandMidName;
    public string leftHandRootName;

    [Toolt<PERSON>("Head Name")]
    public string headName;

    [Toolt<PERSON>("Leg Name")]
    public string rightLegTipName;
    public string rightLegMidName;
    public string rightLegRootName;

    public string leftLegTipName;
    public string leftLegMidName;
    public string leftLegRootName;

    public GameObject vuplexWebViewObject;

    public VR_Rig_Manager vR_Rig_Manager;
    public ChipSelectionManager chipSelectionManager;

    public IEnumerator InitializeBodyIKData()
    {
        // DestroyImmediate(playerCharacterRoot.GetComponent<BoneRenderer>());
        // DestroyImmediate(playerCharacterRoot.GetComponent<RigBuilder>());
        Debug.Log("Initialize IK Data function Triggered  0000");
        yield return new WaitForSeconds(2.0f);   //old 2.0

        Debug.Log("Initialize IK Data function Triggered  1111");
        Transform playerTransformObject = playerCharacterRoot;

        Debug.Log("Player Root Object Name ====> " + playerTransformObject.name);

        rightHandIK.data.tip = HelperClass.GetChildByName(playerTransformObject, rightHandTipName);

        Debug.Log("Player Right Hand Tip Transform Name ====> " + rightHandTipName + " Tranform Object Found ====>  " + rightHandIK.data.tip);

        try
        {
            rightHandIK.data.mid = HelperClass.GetChildByName(playerTransformObject, rightHandMidName);
            rightHandIK.data.root = HelperClass.GetChildByName(playerTransformObject, rightHandRootName);

            leftHandIK.data.tip = HelperClass.GetChildByName(playerTransformObject, leftHandTipName);
            leftHandIK.data.mid = HelperClass.GetChildByName(playerTransformObject, leftHandMidName);
            leftHandIK.data.root = HelperClass.GetChildByName(playerTransformObject, leftHandRootName);

            headIK.data.constrainedObject = HelperClass.GetChildByName(playerTransformObject, headName);
            try
            {
                // rightLegIK.data.tip = HelperClass.GetChildByName(playerTransformObject, rightLegTipName);
                // rightLegIK.data.mid = HelperClass.GetChildByName(playerTransformObject, rightLegMidName);
                // rightLegIK.data.root = HelperClass.GetChildByName(playerTransformObject, rightLegRootName);

                // leftLegIK.data.tip = HelperClass.GetChildByName(playerTransformObject, leftLegTipName);
                // leftLegIK.data.mid = HelperClass.GetChildByName(playerTransformObject, leftLegMidName);
                // leftLegIK.data.root = HelperClass.GetChildByName(playerTransformObject, leftLegRootName);
            }
            catch
            {
                Debug.LogError("Leg IK Data Not Found");
            }
        } catch (System.Exception e) { Debug.LogError(e.ToString()); }
        //  playerCharacterRoot.gameObject.AddComponent<BoneRenderer>();
        //  playerCharacterRoot.gameObject.AddComponent<RigBuilder>();

            Debug.Log("Player Animator Conterller Name:" + PlayerAnimator.runtimeAnimatorController.name);
            //yield return new WaitForSeconds(1.0f);
            //PlayerAnimator.enabled = false;    //Niraj
            playerRootAnimator.enabled = false;
            rigBuilder.Build();
            yield return new WaitForSeconds(0.3f);

            //PlayerAnimator.enabled = true;    //Niraj
            yield return new WaitForSeconds(0.1f);
            playerRootAnimator.enabled = true;  //Niraj new change on 22may 2025

            chipSelectionManager.DefaultHandSatte();


        
        // playerCharacterRoot.gameObject.GetComponent<RigBuilder>().layers.Add(new RigLayer(rig));
        StartCoroutine(DisableWebview());
    }

    public void rigUpdateForWalk()
    {
        playerRootAnimator.enabled = false;
        rigBuilder.Build();  
        rigBuilderPlayer.Build();
        playerRootAnimator.enabled = true;       
    }

    public void HandleFlag()
    {
        Debug.Log("Change Flag:" + vR_Rig_Manager.canUpdate);
        vR_Rig_Manager.canUpdate = !vR_Rig_Manager.canUpdate;
        vR_Rig_Manager.headIK_BodyPosOffset.y = -0.57f;
    }


    public void SetHeadIKTemp()
    {
        vR_Rig_Manager.headIK_BodyPosOffset.y = -0.57f;
    }

    public IEnumerator DisableWebview()
    {
        yield return new WaitForSeconds(5.0f);

        vuplexWebViewObject.SetActive(false);
    }

}