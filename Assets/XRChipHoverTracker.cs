using UnityEngine;
using UnityEngine.XR.Interaction.Toolkit;
using UnityEngine.XR.Interaction.Toolkit.Interactors;
using UnityEngine.XR.Interaction.Toolkit.Interactables;
using System;
using static UnityEngine.Rendering.GPUSort;

public class XRChipHoverTracker : MonoBehaviour
{
    private XRSimpleInteractable _simpleInteractor;
    private ChipInteractor _chipInteractor;

    public string targetGameObjectTag;

    void Awake()
    {

        _simpleInteractor = GetComponent<XRSimpleInteractable>();
        _chipInteractor = GetComponent<ChipInteractor>();

        if (_simpleInteractor != null)
        {
            _simpleInteractor.selectEntered.AddListener(OnSelectEntered);
            _simpleInteractor.selectExited.AddListener(OnSelectExited);

            _simpleInteractor.activated.AddListener(OnActivated);
            _simpleInteractor.activated.AddListener(OnDeactivated);
        }
        else
        {
            Debug.LogError("XRSimpleInteractor component not found!");
        }
    }

    void OnDestroy()
    {
        if (_simpleInteractor != null)
        {
            _simpleInteractor.selectEntered.AddListener(OnSelectEntered);
            _simpleInteractor.selectExited.AddListener(OnSelectExited);

            _simpleInteractor.activated.AddListener(OnActivated);
            _simpleInteractor.activated.AddListener(OnDeactivated);
        }
        else
        {
            Debug.LogError("XRSimpleInteractor component not found!");
        }
    }


    private void OnSelectEntered(SelectEnterEventArgs arg0)
    {
        Debug.Log("Select Entered Triggered");

        GameObject targetObject = arg0.interactorObject.transform.parent.gameObject;

        /* 
        Debug.Log("Selection Entered");
        Debug.Log("Selection Entered Object Name ====> " + targetObject.name);
        Debug.Log("Selection Entered Object Parent Name ====> " + targetObject.transform.parent.gameObject.name);

        Debug.Log("Tag Condition Value ====> " + (!targetObject.tag.Contains(targetGameObjectTag)));
        */


        if (!targetObject.tag.Contains(targetGameObjectTag))
            return;

        _chipInteractor.SelectionState(true, targetObject.tag);
    }

    private void OnSelectExited(SelectExitEventArgs arg0)
    {
        Debug.Log("Select Entered Triggered");

        GameObject targetObject = arg0.interactorObject.transform.parent.gameObject;

        Debug.Log("Selection Exited");
        Debug.Log("Selection Exited Object Name ====> " + targetObject.name);

        Debug.Log("Tag Condition Value ====> " + (!targetObject.tag.Contains(targetGameObjectTag)));

        if (!targetObject.tag.Contains(targetGameObjectTag))
            return;

        Debug.Log("Target Object Name ====> " + targetObject.name + " || Target Object Tag ====> " + targetObject.tag);
        _chipInteractor.SelectionState(false, targetObject.tag);
    }

    private void OnActivated(ActivateEventArgs arg0)
    {
        GameObject targetObject = arg0.interactorObject.transform.parent.gameObject;

        if (!targetObject.tag.Contains(targetGameObjectTag))
            return;

        //Debug.Log("Target Object Name ====> " + targetObject.name + " || Target Object Tag ====> " + targetObject.tag);
        //_chipInteractor.TriggerState(true, targetObject.tag);
    }

    private void OnDeactivated(ActivateEventArgs arg0)
    {
        GameObject targetObject = arg0.interactorObject.transform.parent.gameObject;

        if (!targetObject.tag.Contains(targetGameObjectTag))
            return;


        Debug.Log("Target Object Name ====> " + targetObject.name + " || Target Object Tag ====> " + targetObject.tag);
        _chipInteractor.TriggerState(false, targetObject.tag);
    }
}