using UnityEngine;
using DG.Tweening;
using System.Collections;

//===================================================================================================================================>
// BallManager is the class delegated to manage "Pin Ball" related functionalities in the roulette - Kapil Khatik || Xogo Studios
//===================================================================================================================================>
public class BallManager : MonoBehaviour {
    
    public bool spinning = false;
    public Rigidbody ball;
    public Transform resultPoint;
    public Transform originPoint;

    public Transform pivotTransform;
    public Transform pivotWheelTransform;

    private float ballTimeSpeed = 1.3f;

    public Wheel wheel;

    private Transform Target;

    private static readonly Vector3 axis = Vector3.up;
    private float angularSpeed = 5f;
    private bool stopping = false;

    private Vector3 deltaAngularCross = Vector3.zero;

    private bool trigger_animateBall = true;

    private int res = -1;

    private const int offsetSlots = 3;


    #region Native Method Implementation

    void Start () {
        ball.isKinematic = true;
    }

    private void FixedUpdate()
    {
        if (!spinning)
            return;

        transform.Rotate(axis, angularSpeed);

        #region Old Working Code
        /*  if (stopping)
          {
              Vector3 angularCross = Vector3.Cross(transform.forward, (Target.position - transform.position).normalized);
              float angle = Vector3.SignedAngle(transform.forward, (Target.position - transform.position), transform.up);
              float angleRatio = CalculateAngleRatio(angularCross);
              if (deltaAngularCross.y > 0f)
              {
                  if (angle < 35 && angle > 0)
                      angularSpeed = angleRatio * 2f;

                  if (angleRatio <= 0.2f && trigger_animateBall && angle > 5)
                  {
                      trigger_animateBall = false;
                      PlaceToResult(angleRatio);
                  }
                  else if (angleRatio <= 0.01f && !trigger_animateBall)
                  {
                      spinning = false;
                      transform.SetParent(pivotWheelTransform);
                      ball.isKinematic = false;
                      stopping = false;
                      ResultManager.SetResult(res);
                  }
              }
              Debug.DrawRay(transform.position, angularCross, Color.white);
              Debug.DrawRay(transform.position, (Target.position - transform.position), Color.yellow);
              Debug.DrawRay(ball.transform.position, (Target.position - resultPoint.position), Color.green);
          } */

        #endregion Old Working Code

        if (stopping)
        {
            Vector3 targetDirection = (Target.position - transform.position).normalized;
            float angleToTarget = Vector3.SignedAngle(transform.forward, targetDirection, transform.up);

            // Calculate the ratio based on the current angle to the target
            float angleRatio = Mathf.Abs(angleToTarget) / 180f;

            // Adjust angular speed based on proximity to target, slowing down as we get closer
            angularSpeed = Mathf.Lerp(0.5f, 2f, angleRatio);
            Debug.Log("Angle Ratio ====> " + angleRatio + " || Angular Speed ====> " + angularSpeed + " || Angle To Target ====> " + angleToTarget);
            // Check if the ball is within a precise stopping range of the target
            if (angleRatio <= 0.05f && trigger_animateBall)
            {
                Debug.Log("Ball is in Rang To Stop:");
                trigger_animateBall = false;
                PlaceToResult(angleRatio);
            }
            else if (angleRatio <= 0.018f && !trigger_animateBall)
            {
                Debug.Log("Not in Rang");
                spinning = false;
                transform.SetParent(pivotWheelTransform);
                ball.isKinematic = false;
                stopping = false;
                ResultManager._Instance.SetResult(res);
            }

            // Debug visualizations to verify angles and directions
            Debug.DrawRay(transform.position, targetDirection, Color.yellow);
            Debug.DrawRay(transform.position, transform.forward, Color.white);
        }
    }

    #endregion

    public void StartSpin()
    {
        ball.isKinematic = true;
        ball.transform.SetParent(originPoint);
        ball.transform.localPosition = Vector3.zero;
        transform.SetParent(pivotTransform);
        transform.localRotation = Quaternion.identity;
        angularSpeed = 5;
        spinning = true;
        trigger_animateBall = true;
    }
    
    public void FindNumber(int result, bool isEuropean)
    {
        Debug.Log("Result ====> " + result + " || isEuropean ====> " + isEuropean);
        result = result == -1 && !isEuropean ? 37 : result;
        Target = wheel.resultCheckerObject[result].transform;
        // Adjust the result to account for offset

        int adjustedResult = (result + offsetSlots) % wheel.resultCheckerObject.Length;
        Target = wheel.resultCheckerObject[adjustedResult].transform;

        Debug.Log(" Result Value ====> " + adjustedResult + " || Target Name ====> " + Target.name);
        res = adjustedResult;

        //wheel.rouletteResultManager.ShowResult(adjustedResult);   //commented line i have uncommented for testing

        DOTween.To(() => angularSpeed, x => angularSpeed = x, 1.5f, 5).OnComplete(() =>
        {
            Debug.Log("Stoping here as Number is Finded");
            stopping = true;
            ResultPanelHandler.Inst.StopSpinWheelAnim();
        });
    }

    private bool bouncing = false;
    public void PlaceToResult(float angleRatio)
    {
        ball.transform.SetParent(resultPoint);
        Vector3 direction = (Target.position - resultPoint.position);
        Debug.Log(" Target Position ====> " + Target.position + " || Result Point Position ====> "
            + resultPoint.position + " || Direction ====> " + direction  + " || Result Point ====> " + resultPoint.name);
        bouncing = true;
        Roulette_AudioManager.StopAuxiliar();
        StartCoroutine(BounceSound());
        ball.transform.DOLocalJump(Vector3.zero, .04f, 5, ballTimeSpeed).SetEase(Ease.Linear).OnComplete(() => { bouncing = false; });
        
    }

    private IEnumerator BounceSound()
    {
        while (bouncing)
        {
            yield return new WaitForSeconds(.3f);
            Roulette_AudioManager.SoundPlay(1);
        }
    }

    private float CalculateAngleRatio(Vector3 angularCross)
    {
        deltaAngularCross = angularCross - deltaAngularCross;

        Vector3 targetVector = (Target.position - transform.position);
        Vector3 ballVector = (ball.position - transform.position);

        targetVector.y = ballVector.y = 0;

        return (Vector3.Angle(ballVector, targetVector) / 180f);
    }
}
