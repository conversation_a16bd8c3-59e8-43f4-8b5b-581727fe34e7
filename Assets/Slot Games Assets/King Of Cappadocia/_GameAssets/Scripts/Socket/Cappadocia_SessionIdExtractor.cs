
using System;

using UnityEngine;

[DefaultExecutionOrder(1)]
public class Cappadocia_SessionIdExtractor : MonoBehaviour
{
    //public static SessionIdExtractor instance;
    public string sessionId;
    [SerializeField] internal SocketSc socketSc;


    private void OnEnable()
    {
        if (this.gameObject.transform.parent.gameObject.name == "Crash Game Root Child")
            OnConnectedToServer();
    }

    internal void OnConnectedToServer()
    {
        Connection();
    }

    private async void Connection()
    {
        if (socketSc.socketConnectState == SocketState.None)
        {
            if (Application.platform == RuntimePlatform.WebGLPlayer || Application.platform == RuntimePlatform.OSXEditor)
            {
                string url = Application.absoluteURL;

                //sessionId = url.Split('=')[1];

                //Debug.Log("Session ID Get Form URL ====> " + sessionId);


                //SignatureAPI.inst.GenerateSignature("G1720761376617");
                //while (!SignatureAPI.inst.isDone)
                //{
                //    await Awaitable.WaitForSecondsAsync(2);
                //}
                //url = SignatureAPI.inst.urlResponse.gameUrl;
                //Debug.Log("URL===> " + url);

                //// Make sure the URL is not empty
                //if (!string.IsNullOrEmpty(url))
                //{
                //    // Parse the URL
                //    Uri uri = new Uri(url);
                //    string query = uri.Query;

                //    // Extract the sessionId parameter
                //    var queryParams = System.Web.HttpUtility.ParseQueryString(query);
                //    sessionId = queryParams.Get("sessionId");

                //    // Output the sessionId
                //    if (!string.IsNullOrEmpty(sessionId))
                //    {
                //        Debug.Log("Session ID: " + sessionId);
                //    }
                //    else
                //    {
                //        Debug.Log("Session ID not found in the URL.");
                //    }
                //}
                //else
                //{
                //    Debug.Log("URL is empty.");
                //}
                socketSc.ConnectToSocket();
            }
            else
            {
                socketSc.ConnectToSocket();
            }

        }



    }

}