using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Unity.XR;
using StarterAssets;
using UnityEngine.XR.Interaction.Toolkit;
using UnityEngine.XR.Interaction.Toolkit.Locomotion;
using UnityEngine.XR.Interaction.Toolkit.Locomotion.Turning;
using Metaverse;
using FishNet.Example;

public class GameConfigManager : Singleton<GameConfigManager>
{

    public bool isOnlineMode;
    public bool isEditorTest;

    [Header("VR Platform Component Variables")]
    public Unity.XR.CoreUtils.XROrigin XROrigin;
    public UnityEngine.XR.Interaction.Toolkit.Inputs.InputActionManager inputActionManager;
    //public LocomotionSystem locomotionSystem;
    public LocomotionMediator locomotionMediator;
    //public UnityEngine.XR.Interaction.Toolkit.Samples.StarterAssets.DynamicMoveProvider dynamicMoveProvider;
    //public CharacterControllerDriver characterControllerDriver;
    //public ActionBasedContinuousTurnProvider actionBasedContinuousTurnProvider;
    public ContinuousTurnProvider continuousTurnProvider;
    //public ActionBasedSnapTurnProvider actionBasedSnapTurnProvider;
    //public SnapTurnProvider snapTurnProvider;
    public UnityEngine.XR.Interaction.Toolkit.Locomotion.Teleportation.TeleportationProvider teleportationProvider;


    [Header("VR Rig Components")]
    public VR_Rig_Manager vrRigManager;
    public float vrHeadOffset;

    [Header("Character Controller Componenet Variables")]
    public CharacterController characterController;
    public ThirdPersonController thirdPersonController;

    public GameObject standalonePlayer;
    public GameObject FishnetObj;

    public GameObject UIInputStandalone;

    // Start is called before the first frame update
    void Start()
    {
        ConfigPlayerRunMode();
    }

    // Update is called once per frame
    void Update()
    {
        
    }

    public void ConfigPlayerRunMode()
    {
        if (isEditorTest)
        {
            standalonePlayer.SetActive(true);
            FishnetObj.transform.localScale = Vector3.zero;
            UIInputStandalone.SetActive(true);
            
            GameObject lobby = new GameObject("Lobby");
            lobby.layer = 0; // Default layer
        }
        else
        {
            standalonePlayer.SetActive(false);
            FishnetObj.transform.localScale = Vector3.one;
            UIInputStandalone.SetActive(false);
            
            gameObject.layer = LayerMask.NameToLayer("Ground");
        }
        return;
        Debug.Log("Configuration as per run mode || Run Mode is Editor ====> " + isEditorTest);
        if (isEditorTest)
        {
            XROrigin.enabled = false;
            inputActionManager.enabled = false;
            //locomotionSystem.enabled = false;
            locomotionMediator.enabled = false;
            //dynamicMoveProvider.enabled = false;
            //characterControllerDriver.enabled = false;
            continuousTurnProvider.enabled = false;
            //snapTurnProvider.enabled = false;
            teleportationProvider.enabled = false;

            thirdPersonController.enabled = true;
            vrRigManager.headIK_BodyPosOffset.y = 0f;
        }
        else
        {
            XROrigin.enabled = true;
            inputActionManager.enabled = true;
            locomotionMediator.enabled = true;
            //dynamicMoveProvider.enabled = true;
            //characterControllerDriver.enabled = true;
            continuousTurnProvider.enabled = true;
            //snapTurnProvider.enabled = true;
            teleportationProvider.enabled = true;
            thirdPersonController.enabled = false;
            vrRigManager.headIK_BodyPosOffset.y = vrHeadOffset;
        }
    }
}
