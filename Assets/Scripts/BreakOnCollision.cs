using System.Collections;
using System.Collections.Generic;
using Unitycoder.Demos;
using UnityEngine;

namespace Unitycoder.Demos
{
    public class BreakOnCollision : MonoBehaviour
    {
        [Tooltip("Collision impact threshold")]
        public float breakForce = 5f;
        public GameObject Target, baseObject;

        public Rigidbody rb;
        private bool isBroken = false;
        private void OnCollisionEnter(Collision collision)
        {
            if (isBroken) return;
            if (collision.relativeVelocity.magnitude > breakForce)
            {
                isBroken = true;
                baseObject.SetActive(false);
                Destroy(rb);

                Target.SetActive(true);
                foreach (Transform shard in Target.transform)
                {
                    shard.transform.parent = null;
                }

            }
        }
    }
}
