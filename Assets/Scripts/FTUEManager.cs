using UnityEngine;
using UnityEngine.XR.Interaction.Toolkit.Inputs.Readers;
using UnityEngine.UI;

public class FTUEManager : MonoBehaviour
{
    public static FTUEManager instance;
    public GameObject leftController;
    public GameObject rightController;

    [SerializeField]
    XRInputValueReader<Vector2> left_StickInput = new XRInputValueReader<Vector2>("Thumbstick");

        [SerializeField]
    XRInputValueReader<Vector2> right_StickInput = new XRInputValueReader<Vector2>("Thumbstick");


    public GameObject welcomeScreen;
    public Text titleTxt,ftueMainMsgTxt;
    #region left Controller FTUE
    public GameObject movementFTUEUI;
    #endregion

    #region right Controller FTUE
    public GameObject rotationFTUEUI,teleportFTUEUI;
    public GameObject indexFingarPressFTUE;
    #endregion
    /// <summary>
    /// Awake is called when the script instance is being loaded.
    /// </summary>
    void Awake()
    {
        instance = this;
        Debug.Log("FTUE Manager");
        Invoke(nameof(StartFTUE), 3f);
    }

    public void CompleteFTUE()
    {
        PlayerPrefs.SetString("FTUE", "true");
    }

    void changeMsg(string msg)
    {
        welcomeScreen.SetActive(true);
        ftueMainMsgTxt.text = msg;

    }

    public async void StartFTUE()
    {
        Debug.Log("StartFTUE");
        if (!PlayerPrefs.HasKey("FTUE"))
        {
            Debug.Log("FTUE is Not completed");
            welcomeScreen.SetActive(true);
            await Awaitable.WaitForSecondsAsync(2);
            leftController.SetActive(true);
            rightController.SetActive(true);
            await Awaitable.WaitForSecondsAsync(2);
            titleTxt.text = "";
            changeMsg("now look at left controller");
            movementFTUEUI.SetActive(true);
        }
        else
        {
            Debug.Log("FTUE is completed");
            leftController.SetActive(false);
            rightController.SetActive(false);
        }
    }

    bool movementStep = false;
    public void MovementStepDone()
    {
        movementStep = true;
        // leftController.SetActive(false);
        movementFTUEUI.SetActive(false);
        changeMsg("now look at right controller");
        rotationFTUEUI.SetActive(true);
    }

    bool rotationStep = false;
    bool teleportStep = false;
    public void RotationStepDone()
    {
        rotationStep = true;
        // rightController.SetActive(false);
        rotationFTUEUI.SetActive(false);

        changeMsg("now lets move with teleport");
        teleportFTUEUI.SetActive(true);
    }

    public async void TeleportStepDone()
    {
        teleportStep = true;
        teleportFTUEUI.SetActive(false);   
        leftController.SetActive(false);
        rightController.SetActive(false); 
        changeMsg("now lets explore Wardrobe area");
        await Awaitable.WaitForSecondsAsync(2);
        welcomeScreen.SetActive(false);
        await Awaitable.WaitForSecondsAsync(15);
        changeMsg("now lets go to Lasvegas table stand area");
        await Awaitable.WaitForSecondsAsync(2);
        welcomeScreen.SetActive(false);
    }

    public void IndexFingarPressFTUE()
    {
        if(PlayPrefs.HasKey("FTUE"))
        {
            return;
        }
        indexFingarPressFTUE.SetActive(true);
    }


    /// <summary>
    /// Update is called every frame, if the MonoBehaviour is enabled.
    /// </summary>
    void Update()
    {
        if (left_StickInput != null)
        {
            var stickVal = left_StickInput.ReadValue();
            if ((stickVal.x > 0 || stickVal.y > 0) && !movementStep)
            {
                MovementStepDone();
            }
        }
        if (right_StickInput != null && movementStep)
        {
            var stickVal = right_StickInput.ReadValue();
            if ((stickVal.x > 0) && !rotationStep)
            {
                RotationStepDone();
            }
            else if (stickVal.y > 0 && !teleportStep && rotationStep)
            {
                TeleportStepDone();
            }
        }   
    }
    
}
