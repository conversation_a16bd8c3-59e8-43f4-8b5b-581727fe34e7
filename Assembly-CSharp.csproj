﻿<Project ToolsVersion="Current">
  <!-- Generated file, do not modify, your changes will be overwritten (use AssetPostprocessor.OnGeneratedCSProject) -->
  <PropertyGroup>
    <BaseIntermediateOutputPath>Temp/obj/$(Configuration)/$(MSBuildProjectName)</BaseIntermediateOutputPath>
    <IntermediateOutputPath>$(BaseIntermediateOutputPath)</IntermediateOutputPath>
  </PropertyGroup>
  <Import Project="Sdk.props" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Include="Unity" />
  </ItemGroup>
  <PropertyGroup>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <EnableDefaultItems>false</EnableDefaultItems>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <LangVersion>9.0</LangVersion>
    <Configurations>Debug;Release</Configurations>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <RootNamespace></RootNamespace>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>Assembly-CSharp</AssemblyName>
    <TargetFramework>netstandard2.1</TargetFramework>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp/bin/Debug/</OutputPath>
    <DefineConstants>UNITY_6000_0_25;UNITY_6000_0;UNITY_6000;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_2022_1_OR_NEWER;UNITY_2022_2_OR_NEWER;UNITY_2022_3_OR_NEWER;UNITY_2023_1_OR_NEWER;UNITY_2023_2_OR_NEWER;UNITY_2023_3_OR_NEWER;UNITY_6000_0_OR_NEWER;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_NATIVE_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_EDITOR_GAME_SERVICES;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_ENGINE_CODE_STRIPPING;ENABLE_ONSCREEN_KEYBOARD;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;ENABLE_MARSHALLING_TESTS;ENABLE_VIDEO;ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;ENABLE_ACCESSIBILITY;TEXTCORE_1_0_OR_NEWER;EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED;PLATFORM_ANDROID;UNITY_ANDROID;UNITY_ANDROID_API;ENABLE_EGL;ENABLE_NETWORK;ENABLE_RUNTIME_GI;ENABLE_CRUNCH_TEXTURE_COMPRESSION;UNITY_CAN_SHOW_SPLASH_SCREEN;UNITY_HAS_GOOGLEVR;UNITY_HAS_TANGO;ENABLE_SPATIALTRACKING;ENABLE_ETC_COMPRESSION;PLATFORM_EXTENDS_VULKAN_DEVICE;PLATFORM_HAS_MULTIPLE_SWAPCHAINS;UNITY_ANDROID_SUPPORTS_SHADOWFILES;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;PLATFORM_EXTENDS_VULKAN_PIPELINE_CACHE;PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS;PLATFORM_HAS_ADDITIONAL_API_CHECKS;ENABLE_UNITYADS_RUNTIME;UNITY_UNITYADS_API;ENABLE_MONO;NET_STANDARD_2_0;NET_STANDARD;NET_STANDARD_2_1;NETSTANDARD;NETSTANDARD2_1;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_OSX;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_INPUT_SYSTEM;ENABLE_LEGACY_INPUT_MANAGER;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER;USE_INPUT_SYSTEM_POSE_CONTROL;USE_STICK_CONTROL_THUMBSTICKS;AVATURN;STARTER_ASSETS_PACKAGES_CHECKED;VUPLEX_CCU;VUPLEX_DISABLE_SRP_WARNING;VUPLEX_XR_INTERACTION_TOOLKIT;DOTWEEN;FISHNET;FISHNET_V4;EDGEGAP_PLUGIN_SERVERS;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169;USG0001</NoWarn>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>Temp/bin/Release/</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169;USG0001</NoWarn>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoStandardLibraries>true</NoStandardLibraries>
    <NoStdLib>true</NoStdLib>
    <NoConfig>true</NoConfig>
    <DisableImplicitFrameworkReferences>true</DisableImplicitFrameworkReferences>
    <MSBuildWarningsAsMessages>MSB3277</MSBuildWarningsAsMessages>
  </PropertyGroup>
  <PropertyGroup>
    <UnityProjectGenerator>Package</UnityProjectGenerator>
    <UnityProjectGeneratorVersion>2.0.22</UnityProjectGeneratorVersion>
    <UnityProjectGeneratorStyle>SDK</UnityProjectGeneratorStyle>
    <UnityProjectType>Game:1</UnityProjectType>
    <UnityBuildTarget>Android:13</UnityBuildTarget>
    <UnityVersion>6000.0.25f1</UnityVersion>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="/Users/<USER>/.vscode/extensions/visualstudiotoolsforunity.vstuc-1.1.2/Analyzers/Microsoft.Unity.Analyzers.dll" />
    <Analyzer Include="/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll" />
    <Analyzer Include="/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll" />
    <Analyzer Include="/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll" />
    <Analyzer Include="/Users/<USER>/Documents/Xogo Studios Github/casinoverse/Assets/FishNet/Runtime/Plugins/CodeAnalysis/FishNet.CodeAnalysis.Analyzers.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Assets/Fantastic City Generator/Scripts/TrafficSystem.cs" />
    <Compile Include="Assets/MetaMask/Samples/Main/Scripts/NetworkSwitcher.cs" />
    <Compile Include="Assets/Scripts/GroupChat/Scripts/Singleton/Globals.cs" />
    <Compile Include="Assets/Scripts/Manager/ScreenManager.cs" />
    <Compile Include="Assets/Scripts/EventBuses/GamePlayEventBus.cs" />
    <Compile Include="Assets/Scripts/Others/CineTouch.cs" />
    <Compile Include="Assets/Brick Project Studio/Apartment Kit/Scenes/Quitgame.cs" />
    <Compile Include="Assets/Scripts/Manager/PlayerPrefsManager.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Scripts/Styles/EyeStyle.cs" />
    <Compile Include="Assets/Scene Teleportation Kit/Scripts/teleport/Teleporter.cs" />
    <Compile Include="Assets/TextMesh Pro/Examples &amp; Extras/Scripts/TMP_TextEventHandler.cs" />
    <Compile Include="Assets/MainManager.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/UnityThread.cs" />
    <Compile Include="Assets/Lets Make a VR Game/UI 3D Examples/UI_3D/Scripts/XRGripButton.cs" />
    <Compile Include="Assets/ChipBettingManagement_Package/Scripts/Interfaces/IBetable.cs" />
    <Compile Include="Assets/Scripts/Others/SearchScript.cs" />
    <Compile Include="Assets/Roulette Game Package/RouletteGame/Roulette/Scripts/ResultManager.cs" />
    <Compile Include="Assets/Roulette Game Package/RouletteGame/Roulette/Scripts/AudioEvent.cs" />
    <Compile Include="Assets/Roulette Game Package/RouletteGame/Roulette/Scripts/CanvasFollowCamera.cs" />
    <Compile Include="Assets/Fantastic City Generator/Scripts/Mesh_CombineUtility.cs" />
    <Compile Include="Assets/Scripts/Manager/PlaygameBaseClass.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Demo/Demo Scripts/UI/Pages/DemoPage.cs" />
    <Compile Include="Assets/Samples/Universal RP/17.0.3/URP Package Samples/RendererFeatures/DepthBlit/DepthBlitEdgePass.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Game/Script/MagicBeamScript.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/Transport/Payload.cs" />
    <Compile Include="Assets/Fantastic City Generator/Scripts/TrafficLights.cs" />
    <Compile Include="Assets/Scripts/Manager/PointsManager.cs" />
    <Compile Include="Assets/Scripts/GroupChatScript/GroupMessageWindow.cs" />
    <Compile Include="Assets/Roulette Game Package/RouletteGame/Roulette/Scripts/TabButton.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/Epic Toon FX Crash/Demo/Scripts/VFX Library/PEButtonScript.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/Epic Toon FX Crash/Demo/Scripts/ETFXFireProjectile.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/Scripts/AutoToggleSc.cs" />
    <Compile Include="Assets/MetaMask/Scripts/Utilities/TokenDisplay.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/UnityWebRequestAwaiter.cs" />
    <Compile Include="Assets/Samples/Universal RP/17.0.3/URP Package Samples/RendererFeatures/DepthBlit/DepthBlitCopyDepthPass.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/Messages/ErrorMessage.cs" />
    <Compile Include="Assets/MetaMask/Scripts/Sockets/MetaMaskUnitySocketIO.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Demo/Demo Scripts/UI/Elements/WearableMenuElements.cs" />
    <Compile Include="Assets/Scripts/Gameplay/PlayerAnimationController.cs" />
    <Compile Include="Assets/Brick Project Studio/_BPS Basic Assets/Common/Scripts and Animations/First Person Player/SceneSwitchGen.cs" />
    <Compile Include="Assets/Brick Project Studio/_BPS Basic Assets/Common/Scripts and Animations/Windows/opencloseWindow.cs" />
    <Compile Include="Assets/Scripts/Others/UserFriendListComponent.cs" />
    <Compile Include="Assets/_Barking_Dog/Common Scripts/Turn_Move.cs" />
    <Compile Include="Assets/Scripts/Screens/SubScreens/Me_SubScreen.cs" />
    <Compile Include="Assets/Brick Project Studio/_BPS Basic Assets/Common/Scripts and Animations/First Person Player/MouseLook.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/Scripts/KeyboardManager.cs" />
    <Compile Include="Assets/Fantastic City Generator/Traffic System/DataSpawn.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/Epic Toon FX Crash/Demo/Scripts/VFX Library/UICanvasManager.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Game/Script/GameManager.cs" />
    <Compile Include="Assets/Roulette Game Package/RouletteGame/Roulette/Scripts/GhostStack.cs" />
    <Compile Include="Assets/Scripts/GroupChatScript/GroupMemberPrefabScript.cs" />
    <Compile Include="Assets/Fantastic City Generator/Scripts/TFShiftHand.cs" />
    <Compile Include="Assets/MetaMask/Scripts/MetaMaskUnity.cs" />
    <Compile Include="Assets/TextMesh Pro/Examples &amp; Extras/Scripts/Benchmark02.cs" />
    <Compile Include="Assets/Scene Teleportation Kit/Scripts/player/PlayerCamera.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Game/Script/ButtonEnlarge.cs" />
    <Compile Include="Assets/Brick Project Studio/Apartment Kit/Common/Scripts &amp; Animation/Z Axis -1/Drawer_Pull_Zopp.cs" />
    <Compile Include="Assets/Roulette Game Package/RouletteGame/Roulette/Scripts/Roulette_AudioManager.cs" />
    <Compile Include="Assets/Scripts/Avaturn SDK Integration Scripts/WardrobeSceneManager.cs" />
    <Compile Include="Assets/Scripts/NethereumWalletConnection.cs" />
    <Compile Include="Assets/Scripts/Handler/DayNightHandler.cs" />
    <Compile Include="Assets/Scripts/Screens/CreateNewPassword.cs" />
    <Compile Include="Assets/Brick Project Studio/Apartment Kit/Common/Scripts &amp; Animation/TableFlip/TableFlipR.cs" />
    <Compile Include="Assets/Scripts/Helper/SingletonScriptable.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/WebSocket/AndroidWebSocket.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Scripts/Modular System/HiddenCacheManager.cs" />
    <Compile Include="Assets/Scripts/Handler/GuestAuthenticationHandler.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/Scripts/Cappadocia_GameManager.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Game/GridsLogic/GridManager.cs" />
    <Compile Include="Assets/Fantastic City Generator/Scripts/RunTimeSample.cs" />
    <Compile Include="Assets/Scripts/GroupChat/Scripts/Singleton/Singleton.cs" />
    <Compile Include="Assets/Lets Make a VR Game/UI 3D Examples/UI_3D/Scripts/XRKnob.cs" />
    <Compile Include="Assets/Scripts/Transitions/Teleportation/PassThroughTeleportationManager.cs" />
    <Compile Include="Assets/Scripts/GroupChatScript/GroupInfo.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/Epic Toon FX Crash/Demo/Scripts/ETFXTarget.cs" />
    <Compile Include="Assets/Scripts/Handler/GoogleHandler.cs" />
    <Compile Include="Assets/MetaMask/Samples/Main/Scripts/Utils/Images/ImageDownloader.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/Messages/PongMessage.cs" />
    <Compile Include="Assets/PlayerPostureController.cs" />
    <Compile Include="Assets/Scene Teleportation Kit/Scripts/player/PlayerController.cs" />
    <Compile Include="Assets/_Barking_Dog/Common Scripts/TestMouseRotation.cs" />
    <Compile Include="Assets/Scripts/Handler/APIRequest.cs" />
    <Compile Include="Assets/MetaMask/Scripts/Transports/Unity/UGUI/MetaMaskOTPPanel.cs" />
    <Compile Include="Assets/Roulette Game Package/RouletteGame/Roulette/Scripts/Quit.cs" />
    <Compile Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/Black mage spells/Demo scene/Demo scene files/BlackMageController.cs" />
    <Compile Include="Assets/Roulette Game Package/RouletteGame/Roulette/Scripts/DontDestructableObj.cs" />
    <Compile Include="Assets/MetaMask/Scripts/Transports/Unity/UGUI/MetaMaskUnityUIHandler.cs" />
    <Compile Include="Assets/Roulette Game Package/RouletteGame/Roulette/Scripts/EuropeanWheel.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/Scripts/MyData.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/Messages/BinaryMessage.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/Messages/ClientBinaryAckMessage.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Game/Script/Egyption_SoundManager.cs" />
    <Compile Include="Assets/Black Jack Scene Package/Imported/BlackJack/Project/Multiplayer Blackjack/Blackjack Game/Scenes/Scripts/Blackjack_PlayerController.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/Scripts/BettingAmountSc.cs" />
    <Compile Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/Toon Projectiles 2/Demo scene/DemoShooting.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/Epic Toon FX Crash/Scripts/ETFXPitchRandomizer.cs" />
    <Compile Include="Assets/Scripts/GroupChatScript/GroupMembersPanel.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/Scripts/SpaceShipSc.cs" />
    <Compile Include="Assets/Scripts/Manager/API_Classes.cs" />
    <Compile Include="Assets/ChipBettingManagement_Package/Scripts/Managers/BetManager/BetPool.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/WebSocketDispatcher.cs" />
    <Compile Include="Assets/Brick Project Studio/_BPS Basic Assets/Common/Scripts and Animations/Doors/opencloseDoor1.cs" />
    <Compile Include="Assets/TestChipEvents.cs" />
    <Compile Include="Assets/Samples/Universal RP/17.0.3/URP Package Samples/RendererFeatures/DistortTunnel/DistortTunnelPass_CopyColor.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/Scripts/UserData/UserTopDataPrefab.cs" />
    <Compile Include="Assets/TextMesh Pro/Examples &amp; Extras/Scripts/VertexShakeB.cs" />
    <Compile Include="Assets/Scripts/Screens/SettingPanel_Screen.cs" />
    <Compile Include="Assets/ToonKids/Scripts/TKBoyPrefabMaker.cs" />
    <Compile Include="Assets/Scripts/Managers/CameraManager.cs" />
    <Compile Include="Assets/ToonKids/Scenes/Data/Destroyer.cs" />
    <Compile Include="Assets/MetaMask/Scripts/IO/MetaMaskWebLocalStorage.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOUnity.cs" />
    <Compile Include="Assets/RootManager.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/Messages/DisconnectedMessage.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/Scripts/BettingUiMainPanel.cs" />
    <Compile Include="Assets/Scripts/Screens/SignUp_Screen.cs" />
    <Compile Include="Assets/Scripts/GroupChatScript/AddFriendPrefabScript.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Game/Script/AutoSpinSetting.cs" />
    <Compile Include="Assets/Scene Teleportation Kit/Scripts/teleport/PortTrigger.cs" />
    <Compile Include="Assets/ToonKids/Scripts/TK2DController.cs" />
    <Compile Include="Assets/MetaMask/Scripts/Utilities/JSCallback.cs" />
    <Compile Include="Assets/MetaMask/Scripts/Transports/Unity/MetaMaskUnityScriptableObjectTransport.cs" />
    <Compile Include="Assets/VR_Rig_Manager.cs" />
    <Compile Include="Assets/Scripts/Gameplay/PayerController.cs" />
    <Compile Include="Assets/Scripts/PlayerManagers/PlayerController.cs" />
    <Compile Include="Assets/Scripts/GameConfigManager.cs" />
    <Compile Include="Assets/ChipBettingManagement_Package/Scripts/DataClasses/ChipType.cs" />
    <Compile Include="Assets/TextMesh Pro/Examples &amp; Extras/Scripts/VertexJitter.cs" />
    <Compile Include="Assets/Wristwatch/Scripts/RealClock.cs" />
    <Compile Include="Assets/Scripts/Manager/UserDataManager.cs" />
    <Compile Include="Assets/Scripts/Screens/Notification_Screen.cs" />
    <Compile Include="Assets/Scripts/Screens/BASE_SCREEN.cs" />
    <Compile Include="Assets/Scripts/Others/Interactables.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/Scripts/Socket/SocketSc.cs" />
    <Compile Include="Assets/PlayFlowCloud/Scripts/PlayFlowBackend.cs" />
    <Compile Include="Assets/TextMesh Pro/Examples &amp; Extras/Scripts/VertexZoom.cs" />
    <Compile Include="Assets/MetaMask/Samples/Main/Scripts/Visuals/BackgroundShape.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/Transport/BaseTransport.cs" />
    <Compile Include="Assets/ToonKids/Scripts/TKBoyPrefabMaker_VR.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Game/Script/BetHandle.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Game/Script/SlotReel.cs" />
    <Compile Include="Assets/MetaMask/Scripts/IO/MetaMaskHttpService.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Demo/Demo Scripts/UI/Elements/RuntimeSaveManager.cs" />
    <Compile Include="Assets/StarterAssets/Mobile/Scripts/Utilities/MobileDisableAutoSwitchControls.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Game/Script/CongratulationsHandler.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/Scripts/HomeManager.cs" />
    <Compile Include="Assets/TextMesh Pro/Examples &amp; Extras/Scripts/SkewTextExample.cs" />
    <Compile Include="Assets/Scripts/Screens/General_Script.cs" />
    <Compile Include="Assets/TextMesh Pro/Examples &amp; Extras/Scripts/WarpTextExample.cs" />
    <Compile Include="Assets/Scripts/Transitions/Teleportation/Teleporter.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/Scripts/DepositSettings.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Game/Script/WinFillBox.cs" />
    <Compile Include="Assets/TextMesh Pro/Examples &amp; Extras/Scripts/ShaderPropAnimator.cs" />
    <Compile Include="Assets/Samples/Universal RP/17.0.3/URP RenderGraph Samples/RendererList/RendererListRenderFeature.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/Messages/OpenedMessage.cs" />
    <Compile Include="Assets/MetaMask/Scripts/Utilities/UnityBinder.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Demo/Demo Scripts/UI/Buttons/WearableButton.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Demo/Demo Scripts/UI/Pages/CharacterPage.cs" />
    <Compile Include="Assets/Scripts/Screens/SubScreens/Friend_SubScreen.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/Scripts/UserData/AllBetData.cs" />
    <Compile Include="Assets/Samples/Universal RP/17.0.3/URP Package Samples/RendererFeatures/BlitToRTHandle/BlitToRTHandleRendererFeature.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Game/Script/Gradient.cs" />
    <Compile Include="Assets/Scripts/Screens/Gameplay_Screen.cs" />
    <Compile Include="Assets/Samples/Universal RP/17.0.3/URP Package Samples/RendererFeatures/BlitToRTHandle/BlitToRTHandlePass.cs" />
    <Compile Include="Assets/FishNet/Upgrading/MirrorUpgrade.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/Scripts/UserData/UserBetData_MyData.cs" />
    <Compile Include="Assets/Roulette Game Package/RouletteGame/Roulette/Scripts/AvatarInfo.cs" />
    <Compile Include="Assets/Scripts/IgnoreParentRotation.cs" />
    <Compile Include="Assets/Scripts/Others/RotateWithTouchAndMouse.cs" />
    <Compile Include="Assets/StarterAssets/ThirdPersonController/Scripts/ThirdPersonController.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Scripts/General/WearableIndices.cs" />
    <Compile Include="Assets/WallPreventer.cs" />
    <Compile Include="Assets/MetaMask/Scripts/Utilities/WaitForTask.cs" />
    <Compile Include="Assets/MetaMask/Samples/Main/Scripts/Utils/Images/ImageDownloadManager.cs" />
    <Compile Include="Assets/MetaMask/Samples/Main/Scripts/Tokens/TokenList.cs" />
    <Compile Include="Assets/Scripts/ScriptableSingletons/GameSetting.cs" />
    <Compile Include="Assets/Scripts/GroupChatScript/PostPrefabScript.cs" />
    <Compile Include="Assets/PlayFlowCloud/Scripts/PlayFlowMatchMaker.cs" />
    <Compile Include="Assets/Roulette Game Package/RouletteGame/Roulette/Scripts/Managers/RouletteResultManager.cs" />
    <Compile Include="Assets/Scripts/XRRigPersistent.cs" />
    <Compile Include="Assets/TextMesh Pro/Examples &amp; Extras/Scripts/VertexShakeA.cs" />
    <Compile Include="Assets/Brick Project Studio/Apartment Kit/Common/Scripts &amp; Animation/Slide/BRGlassDoor.cs" />
    <Compile Include="Assets/MetaMask/Samples/Main/Scripts/UIModal.cs" />
    <Compile Include="Assets/SeatManager.cs" />
    <Compile Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/Black mage spells/Scripts/TargetProjectile.cs" />
    <Compile Include="Assets/ChipBettingManagement_Package/Scripts/Chip/ChipHighlightFixer.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/JsonSerializer/IJsonSerializer.cs" />
    <Compile Include="Assets/MetaMask/Scripts/MetaMaskUnityEventHandler.cs" />
    <Compile Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/Toon Projectiles 2/Scripts/ProjectileMover.cs" />
    <Compile Include="Assets/Scripts/BlockchainManager/EthRequestsStorageSO.cs" />
    <Compile Include="Assets/Fantastic City Generator/DayNight/ShiftAtRuntime.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/Scripts/Socket/GameUtility.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Game/Script/MultiBack.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/Scripts/UserData/Top Number_TimeDataSc.cs" />
    <Compile Include="Assets/Black Jack Scene Package/Imported/BlackJack/Project/Multiplayer Blackjack/Blackjack Game/Scenes/Scripts/BlackJack_ChipsHandler.cs" />
    <Compile Include="Assets/TextMesh Pro/Examples &amp; Extras/Scripts/Benchmark01_UGUI.cs" />
    <Compile Include="Assets/TextMesh Pro/Examples &amp; Extras/Scripts/SimpleScript.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Scripts/Wearables/Wearable.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/Epic Toon FX Crash/Demo/Scripts/ETFXEffectController.cs" />
    <Compile Include="Assets/TextMesh Pro/Examples &amp; Extras/Scripts/Benchmark04.cs" />
    <Compile Include="Assets/MetaMask/Scripts/MetaMaskUnityEventListener.cs" />
    <Compile Include="Assets/MetaMask/Scripts/Logging/MetaMaskUnityLogger.cs" />
    <Compile Include="Assets/MetaMask/Scripts/Transports/Unity/UGUI/MetaMaskUnityUIQRImage.cs" />
    <Compile Include="Assets/TextMesh Pro/Examples &amp; Extras/Scripts/ChatController.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/Transport/Eio4HttpPollingHandler.cs" />
    <Compile Include="Assets/MetaMask/Samples/Main/Scripts/ModalPrefabs.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/Transport/HttpTransport.cs" />
    <Compile Include="Assets/ServerManager.cs" />
    <Compile Include="Assets/Samples/Universal RP/17.0.3/URP Package Samples/RendererFeatures/DepthBlit/DepthBlitDepthOnlyPass.cs" />
    <Compile Include="Assets/PalmTreePack/Scripts/LB_CameraMove.cs" />
    <Compile Include="Assets/Brick Project Studio/Apartment Kit/Common/Scripts &amp; Animation/Slide/opencloseSlide.cs" />
    <Compile Include="Assets/Scripts/Others/Chat_Bubble.cs" />
    <Compile Include="Assets/Scripts/SceneTransitionManager.cs" />
    <Compile Include="Assets/TextMesh Pro/Examples &amp; Extras/Scripts/TMP_FrameRateCounter.cs" />
    <Compile Include="Assets/Samples/Universal RP/17.0.3/URP RenderGraph Samples/Blit w. FrameData/BlitRendererFeature.cs" />
    <Compile Include="Assets/Scripts/Handler/GameInstanceHandler.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/Transport/WebSocketTransport.cs" />
    <Compile Include="Assets/Black Jack Scene Package/Imported/BlackJack/Project/Multiplayer Blackjack/Blackjack Game/Scenes/Scripts/Blackjack_UIManager.cs" />
    <Compile Include="Assets/MetaMask/Samples/Main/Scripts/TokenBalanceText.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Demo/Demo Scripts/UI/Shifters/ToggleShifter.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Scripts/Sets/WearableSet.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/SocketIOResponse.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Scripts/Styles/EyebrowStyle.cs" />
    <Compile Include="Assets/ChipBettingManagement_Package/Scripts/HelperClasses/ChipCalculator.cs" />
    <Compile Include="Assets/Samples/Universal RP/17.0.3/URP Package Samples/SharedAssets/Scripts/CheckAssignedRenderPipelineAsset.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Scripts/General/GlobalReferences.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Scripts/Sets/WearableSetData.cs" />
    <Compile Include="Assets/Scripts/Others/Loading.cs" />
    <Compile Include="Assets/XRHoverTracker.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/Scripts/Socket/ITopHandler.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/Transport/NativeClientWebSocket.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/Aditya/Chat System/Message Resources/DynamicUISize.cs" />
    <Compile Include="Assets/Lets Make a VR Game/Oculus Hands/Scripts/AnimateHandOnInput.cs" />
    <Compile Include="Assets/MetaMask/Scripts/IO/MetaMaskUnityStorage.cs" />
    <Compile Include="Assets/Roulette Game Package/RouletteGame/Roulette/Scripts/ToolTipManager.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Scripts/Modular System/ModularCharacterManager.cs" />
    <Compile Include="Assets/Scripts/SetOptionFromUI.cs" />
    <Compile Include="Assets/Scene Teleportation Kit/Scripts/teleport/SpawnPoint.cs" />
    <Compile Include="Assets/Fantastic City Generator/WayTool/FCGWaypointsContainer.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/JsonSerializer/JsonSerializeResult.cs" />
    <Compile Include="Assets/Scripts/x43kqbwadadadadw.cs" />
    <Compile Include="Assets/Black Jack Scene Package/Imported/BlackJack/Project/Multiplayer Blackjack/Blackjack Game/Scenes/Scripts/Blackjack_OnClick.cs" />
    <Compile Include="Assets/XRChipHoverTracker.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/Scripts/WinDetailsPanel.cs" />
    <Compile Include="Assets/Scripts/Handler/DataHandler.cs" />
    <Compile Include="Assets/Scripts/GameStartMenu.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/Messages/IMessage.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/Transport/HttpPollingHandler.cs" />
    <Compile Include="Assets/ToonKids/Scripts/Playanimation.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Demo/Demo Scripts/UI/Shifters/ButtonShifter.cs" />
    <Compile Include="Assets/Scripts/Web3/Wallet/Web3_Wallet_Manager.cs" />
    <Compile Include="Assets/Fantastic City Generator/Scripts/FPSDisplay.cs" />
    <Compile Include="Assets/Samples/Universal RP/17.0.3/URP Package Samples/RendererFeatures/KeepFrame/KeepFrameFeature.cs" />
    <Compile Include="Assets/Roulette Game Package/RouletteGame/Roulette/Scripts/ResultPanelHandler.cs" />
    <Compile Include="Assets/Scripts/ScriptableSingletons/AvatarConfig.cs" />
    <Compile Include="Assets/Brick Project Studio/_BPS Basic Assets/Common/Scripts and Animations/Doors/opencloseStallDoor.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/Scripts/UTCTimeSc.cs" />
    <Compile Include="Assets/Slot Games Assets/ChipStackManager.cs" />
    <Compile Include="Assets/MetaMask/Scripts/Contracts/ScriptableERC1155.cs" />
    <Compile Include="Assets/MetaMask/Samples/Test/TestScript.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Game/Script/MusicManager.cs" />
    <Compile Include="Assets/StarterAssets/Mobile/Scripts/CanvasInputs/UICanvasControllerInput.cs" />
    <Compile Include="Assets/Scripts/Others/ChatPanel.cs" />
    <Compile Include="Assets/Scripts/GroupChatScript/GroupPrefabScript.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Scripts/Styles/StyleObject.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Demo/Demo Scripts/UI/BuilderDemoUI.cs" />
    <Compile Include="Assets/Scripts/EventBuses/GeneralEventBus.cs" />
    <Compile Include="Assets/Scripts/Screens/FriendAndFamily_Screen.cs" />
    <Compile Include="Assets/SimpleJSONUnity.cs" />
    <Compile Include="Assets/Scripts/GroupChatScript/PendingFriendScript.cs" />
    <Compile Include="Assets/MetaMask/Scripts/Providers/JsSDKProvider.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/Scripts/UserData/HistoryObjectMultiplier.cs" />
    <Compile Include="Assets/Scripts/Helper/Timer.cs" />
    <Compile Include="Assets/TextMesh Pro/Examples &amp; Extras/Scripts/ObjectSpin.cs" />
    <Compile Include="Assets/Samples/Universal RP/17.0.3/URP RenderGraph Samples/GlobalGbuffers/GlobalGbuffersRendererFeature.cs" />
    <Compile Include="Assets/Samples/Universal RP/17.0.3/URP RenderGraph Samples/UnsafePass/UnsafePassRenderFeature.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Scripts/Styles/CharacterStyle.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Demo/Demo Scripts/UI/Shifters/StyleShifter.cs" />
    <Compile Include="Assets/Roulette Game Package/RouletteGame/Roulette/Scripts/TabGroup.cs" />
    <Compile Include="Assets/Black Jack Scene Package/Imported/BlackJack/Project/Multiplayer Blackjack/Blackjack Game/Scenes/Scripts/Blackjack_BotManager.cs" />
    <Compile Include="Assets/PalmTreePack/Offer/Asset_Store_Offers.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Game/Script/SessionIdExtractor.cs" />
    <Compile Include="Assets/Scripts/Handler/SceneHandler.cs" />
    <Compile Include="Assets/TextMesh Pro/Examples &amp; Extras/Scripts/TextConsoleSimulator.cs" />
    <Compile Include="Assets/TextMesh Pro/Examples &amp; Extras/Scripts/TeleType.cs" />
    <Compile Include="Assets/Fantastic City Generator/Player/Player/CharacterControl.cs" />
    <Compile Include="Assets/Scripts/Extension/GeneralExtension.cs" />
    <Compile Include="Assets/Scripts/GroupChatScript/AddFriendScript.cs" />
    <Compile Include="Assets/Slot Games Assets/SignatureAPI.cs" />
    <Compile Include="Assets/TextMesh Pro/Examples &amp; Extras/Scripts/TMP_TextEventCheck.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Demo/Demo Scripts/Editor Helpers/CreateWearableHelper.cs" />
    <Compile Include="Assets/Scripts/Screens/MainMenu_Screen.cs" />
    <Compile Include="Assets/Scripts/Manager/UGSBaseClass.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/Scripts/AIPlanateAnimation.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Game/Script/MathchItemText.cs" />
    <Compile Include="Assets/Scripts/Screens/About_Screen.cs" />
    <Compile Include="Assets/MetaMask/Samples/Main/Scripts/UIModalManager.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/UriConverters/UriConverter.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient.Newtonsoft.Json/ByteArrayConverter.cs" />
    <Compile Include="Assets/Brick Project Studio/_BPS Basic Assets/Common/Scripts and Animations/Windows/opencloseWindow1.cs" />
    <Compile Include="Assets/Scripts/Screens/Account_Screen.cs" />
    <Compile Include="Assets/Fantastic City Generator/Scripts/CityGenerator.cs" />
    <Compile Include="Assets/MetaMask/Scripts/Transports/Unity/UGUI/MetaMaskUnityUITransport.cs" />
    <Compile Include="Assets/MetaMask/Scripts/Cryptography/UnityEciesProvider.cs" />
    <Compile Include="Assets/Scripts/Others/Interactable_Teleport.cs" />
    <Compile Include="Assets/Brick Project Studio/Apartment Kit/Common/Scripts &amp; Animation/Windows/opencloseWindowApt.cs" />
    <Compile Include="Assets/ChipSelectionManager.cs" />
    <Compile Include="Assets/Scripts/Helper/Singleton.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/JsonSerializer/ByteArrayConverter.cs" />
    <Compile Include="Assets/ToonKids/Scenes/Data/Spawner.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Game/Script/ViewController.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/Epic Toon FX Crash/Demo/Scripts/VFX Library/ParticleEffectsLibrary.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Game/Script/BeamManager.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Scripts/Modular System/ModularCharacter.cs" />
    <Compile Include="Assets/Samples/Avaturn.WebView/2.0.0/WebGL and Mobile/Runtime/_Data/Plugins/Environment/Scripts/MaterialsSetup.cs" />
    <Compile Include="Assets/Brick Project Studio/Apartment Kit/Common/Scripts &amp; Animation/OvenOpen/OvenFlip.cs" />
    <Compile Include="Assets/Scripts/Gameplay/PlayerRotation.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/Transport/TransportMessageType.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/Epic Toon FX Crash/Demo/Scripts/ETFXEffectControllerPooled.cs" />
    <Compile Include="Assets/StarterAssets/InputSystem/StarterAssetsInputs.cs" />
    <Compile Include="Assets/Scripts/Test.cs" />
    <Compile Include="Assets/Scripts/BlockchainManager/SwitchChain.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/Scripts/AutoPlayMainPanelSc.cs" />
    <Compile Include="Assets/Scripts/FadeScreen.cs" />
    <Compile Include="Assets/Scripts/Manager/DebugManager.cs" />
    <Compile Include="Assets/Samples/Universal RP/17.0.3/URP RenderGraph Samples/Compute/ComputeRendererFeature.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Scripts/Wearables/Wearable_Variant.cs" />
    <Compile Include="Assets/MetaMask/Samples/Main/Scripts/TokenPanel.cs" />
    <Compile Include="Assets/Lets Make a VR Game/UI 3D Examples/UI_3D/Scripts/XRLever.cs" />
    <Compile Include="Assets/MetaMask/Samples/Main/Scripts/MetaMaskDemo.cs" />
    <Compile Include="Assets/Black Jack Scene Package/Imported/BlackJack/Project/Multiplayer Blackjack/Blackjack Game/Scenes/Scripts/Blackjack_EventManager.cs" />
    <Compile Include="Assets/Roulette Game Package/RouletteGame/Roulette/Scripts/SceneRoulette.cs" />
    <Compile Include="Assets/_Barking_Dog/Common Scripts/MouseLook.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/EventHandlers.cs" />
    <Compile Include="Assets/Scripts/Handler/CharacterUsedHandler.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Demo/Demo Scripts/UI/Buttons/SwatchButton.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/Messages/EventMessage.cs" />
    <Compile Include="Assets/Scripts/Manager/UGSAuthenticationManager.cs" />
    <Compile Include="Assets/Lets Make a VR Game/UI 3D Examples/UI_3D/Scripts/ClawMachine/FlippyDoor.cs" />
    <Compile Include="Assets/Scripts/Others/Interactable_Door.cs" />
    <Compile Include="Assets/Scripts/GroupChatScript/GroupInvitePrefabScript.cs" />
    <Compile Include="Assets/SacredPlace/Misc/_DynamicClouds/cloud_self.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Demo/Demo Scripts/UI/Pages/WearablePage.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/Scripts/UserData/UserDetails.cs" />
    <Compile Include="Assets/TextMesh Pro/Examples &amp; Extras/Scripts/EnvMapAnimator.cs" />
    <Compile Include="Assets/Scripts/Constants/ApiConstants.cs" />
    <Compile Include="Assets/MetaMask/Samples/Main/Scripts/Utils/UnityDebugger.cs" />
    <Compile Include="Assets/MetaMask/Samples/Main/Scripts/Visuals/LookAtCamera.cs" />
    <Compile Include="Assets/MetaMask/Scripts/Contracts/ScriptableContract.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Game/Script/ErrorPanel.cs" />
    <Compile Include="Assets/Fantastic City Generator/Scripts/TFShiftHand2.cs" />
    <Compile Include="Assets/TextMesh Pro/Examples &amp; Extras/Scripts/TMP_TextSelector_A.cs" />
    <Compile Include="Assets/MetaMask/Samples/Main/Scripts/Utils/Images/ImageHelper.cs" />
    <Compile Include="Assets/Scripts/ScreenTransitionEffect.cs" />
    <Compile Include="Assets/StarterAssets/Mobile/Scripts/VirtualInputs/UIVirtualTouchZone.cs" />
    <Compile Include="Assets/Lets Make a VR Game/UI 3D Examples/UI_3D/Scripts/ClawMachine/UfoAbductionForce.cs" />
    <Compile Include="Assets/Samples/Universal RP/17.0.3/URP Package Samples/CameraStacking/3D Skybox/Scripts/SkyboxCamera.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/Epic Toon FX Crash/Scripts/ETFXRotation.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient.Newtonsoft.Json/NewtonsoftJsonSerializer.cs" />
    <Compile Include="Assets/HeadBasedCharacterAdjuster.cs" />
    <Compile Include="Assets/MetaMask/Samples/Main/Scripts/Tokens/INFTHolder.cs" />
    <Compile Include="Assets/Scripts/GroupChatScript/AddGroupScript.cs" />
    <Compile Include="Assets/Scripts/SplashScreen.cs" />
    <Compile Include="Assets/ChipBettingManagement_Package/Scripts/Chip/Chip.cs" />
    <Compile Include="Assets/Scripts/ScriptableSingletons/LevelData.cs" />
    <Compile Include="Assets/Scripts/Screens/Setting_Screen.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/Epic Toon FX Crash/Demo/Scripts/ETFXButtonScript.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/Scripts/Cappadocia_UIManager.cs" />
    <Compile Include="Assets/ToonKids/Scripts/RagDoll.cs" />
    <Compile Include="Assets/Blackjack_BetPool.cs" />
    <Compile Include="Assets/Roulette Game Package/RouletteGame/Roulette/Scripts/BallManager.cs" />
    <Compile Include="Assets/Samples/Universal RP/17.0.3/URP RenderGraph Samples/MRT/MrtRendererFeature.cs" />
    <Compile Include="Assets/Scripts/HelperClass.cs" />
    <Compile Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/Black mage spells/Demo scene/Demo scene files/HS_CameraShaker.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Demo/Demo Scripts/Editor Helpers/AddWearableSet.cs" />
    <Compile Include="Assets/Lets Make a VR Game/UI 3D Examples/UI_3D/Scripts/ClawMachine/ClawMachine.cs" />
    <Compile Include="Assets/Slot Games Assets/ChipInteraction.cs" />
    <Compile Include="Assets/Scripts/XRInteractionFixer.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Scripts/Wearables/Wearable_Attachment.cs" />
    <Compile Include="Assets/StarterAssets/Mobile/Scripts/VirtualInputs/UIVirtualJoystick.cs" />
    <Compile Include="Assets/Scripts/Others/Interactable_Chair.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/UriConverters/IUriConverter.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/Transport/Eio3HttpPollingHandler.cs" />
    <Compile Include="Assets/Scripts/ScriptableSingletons/PlayerData.cs" />
    <Compile Include="Assets/ToonKids/Scenes/Data/blendshapesshow.cs" />
    <Compile Include="Assets/Scripts/Screens/MetaverseUI/Profile_Screen.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/DisconnectReason.cs" />
    <Compile Include="Assets/FishNet/Upgrading/UpgradeFromMirrorMenu.cs" />
    <Compile Include="Assets/MetaMask/Scripts/MetaMaskConfig.cs" />
    <Compile Include="Assets/MetaMask/Samples/Main/Scripts/TokenBalanceTextMeshPro.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Game/Script/GamePlay.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Game/Script/SocketIOManager.cs" />
    <Compile Include="Assets/MetaMask/Scripts/Transports/Unity/IMetaMaskUnityTransportListener.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Demo/Demo Scripts/UI/Pages/MorphPage.cs" />
    <Compile Include="Assets/MetaMask/Samples/Main/Scripts/Visuals/ButtonEffect.cs" />
    <Compile Include="Assets/Brick Project Studio/_BPS Basic Assets/Common/Scripts and Animations/Drawer/X Axis/Drawer_Pull_X.cs" />
    <Compile Include="Assets/SacredPlace/Misc/Scripts/FPMouseLook.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/WebSocket/IosWebSocket.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/JSON/VectorTemplates.cs" />
    <Compile Include="Assets/Fantastic City Generator/Scripts/TrafficCar.cs" />
    <Compile Include="Assets/ToonKids/Scripts/TKGirlPrefabMaker.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/Messages/MessageFactory.cs" />
    <Compile Include="Assets/Scripts/Handler/ApiHandler.cs" />
    <Compile Include="Assets/SimpleJSONBinary.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Demo/Demo Scripts/UI/Elements/SaveSlot.cs" />
    <Compile Include="Assets/Scripts/Transitions/Teleportation/ITeleportable.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Demo/Demo Scripts/UI/Pages/CosmeticPage.cs" />
    <Compile Include="Assets/Scripts/ScriptableSingletons/AppSettings.cs" />
    <Compile Include="Assets/Scripts/DynamicAttachPoint.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/Transport/SystemNetWebSocketsClientWebSocket.cs" />
    <Compile Include="Assets/MetaMask/Samples/Main/Scripts/Utils/UnityBinder.cs" />
    <Compile Include="Assets/Scripts/Handler/PlayGameHandler.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/Epic Toon FX Crash/Demo/Scripts/ETFXLoopScript.cs" />
    <Compile Include="Assets/MetaMask/Scripts/Contracts/ScriptableERC20.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/Epic Toon FX Crash/Demo/Scripts/ETFXEffectCycler.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/SocketIO.cs" />
    <Compile Include="Assets/TextMesh Pro/Examples &amp; Extras/Scripts/TMP_ExampleScript_01.cs" />
    <Compile Include="Assets/TextMesh Pro/Examples &amp; Extras/Scripts/TMPro_InstructionOverlay.cs" />
    <Compile Include="Assets/Scripts/Others/Interactable_Elevator.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/SocketIOOptions.cs" />
    <Compile Include="Assets/Scene Teleportation Kit/Scripts/player/PlayerManager.cs" />
    <Compile Include="Assets/Roulette Game Package/RouletteGame/Roulette/Scripts/BalanceManager.cs" />
    <Compile Include="Assets/Samples/Universal RP/17.0.3/URP Package Samples/RendererFeatures/DistortTunnel/DistortTunnelRendererFeature.cs" />
    <Compile Include="Assets/Scripts/UIAudio.cs" />
    <Compile Include="Assets/VR Body/IKFootSolver.cs" />
    <Compile Include="Assets/Roulette Game Package/RouletteGame/Roulette/Scripts/LimitBetPlate.cs" />
    <Compile Include="Assets/GabrielAguiarProductions/Scripts/SimpleCameraController.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Game/Script/GameData.cs" />
    <Compile Include="Assets/Scripts/GroupChatScript/ReplyPostPanel.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/Messages/ConnectedMessage.cs" />
    <Compile Include="Assets/Roulette Game Package/RouletteGame/Roulette/Scripts/CameraController.cs" />
    <Compile Include="Assets/ToonKids/Scenes/Data/animatorshow.cs" />
    <Compile Include="Assets/Scripts/Screens/ForgotPassword_Screen.cs" />
    <Compile Include="Assets/ChipBettingManagement_Package/Scripts/Managers/ChipStackManager/ChipStacksHolder.cs" />
    <Compile Include="Assets/Roulette Game Package/RouletteGame/Roulette/Scripts/DollyManager.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/EncryptDecryptID/EncryptDecryptID.cs" />
    <Compile Include="Assets/Slot Games Assets/ButtonFingerInteraction.cs" />
    <Compile Include="Assets/Scripts/ScriptableSingletons/AllAvatarConfig.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/Scripts/Investment_BtnAmountSc.cs" />
    <Compile Include="Assets/Samples/Universal RP/17.0.3/URP Package Samples/LensFlares/Scripts/ScreenSpacePlacement.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/Scripts/KeyboardLayout.cs" />
    <Compile Include="Assets/Scripts/GroupChatScript/YourGroupsScript.cs" />
    <Compile Include="Assets/Scripts/Transitions/Teleportation/TransitionPanel.cs" />
    <Compile Include="Assets/Black Jack Scene Package/Imported/BlackJack/Project/Multiplayer Blackjack/Blackjack Game/Scenes/Scripts/AnimationTigger.cs" />
    <Compile Include="Assets/Roulette Game Package/RouletteGame/Roulette/Scripts/RoomButton.cs" />
    <Compile Include="Assets/MetaMask/Samples/Main/Scripts/Tokens/Metadata.cs" />
    <Compile Include="Assets/MetaMask/Scripts/Utilities/UnityWebRequestAwaiter.cs" />
    <Compile Include="Assets/Fantastic City Generator/DayNight/DayNight.cs" />
    <Compile Include="Assets/Scripts/Others/TouchField.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/Epic Toon FX Crash/Demo/Scripts/ETFXProjectileScript.cs" />
    <Compile Include="Assets/TextMesh Pro/Examples &amp; Extras/Scripts/TMP_UiFrameRateCounter.cs" />
    <Compile Include="Assets/Lets Make a VR Game/UI 3D Examples/UI_3D/Scripts/XRJoystick.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Game/Script/RulesPanel.cs" />
    <Compile Include="Assets/Black Jack Scene Package/Imported/BlackJack/Project/Multiplayer Blackjack/Blackjack Game/Scenes/Scripts/TriggerChecking.cs" />
    <Compile Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/Sword slash VFX/Demo scene/CameraHolder.cs" />
    <Compile Include="Assets/Scripts/Screens/SubScreens/Family_SubScreen.cs" />
    <Compile Include="Assets/Scripts/Handler/GamePlayHandler.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Game/Script/BigWinPanel.cs" />
    <Compile Include="Assets/Scene Teleportation Kit/Scripts/teleport/Teleportable.cs" />
    <Compile Include="Assets/TextMesh Pro/Examples &amp; Extras/Scripts/TMP_TextSelector_B.cs" />
    <Compile Include="Assets/VRBodyRotationSimple.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Demo/Demo Scripts/UI/Shifters/LabelShifter.cs" />
    <Compile Include="Assets/MetaMask/Scripts/Utilities/Singleton.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/Exceptions/UnityWebRequestException.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Scripts/Styles/SkinStyle.cs" />
    <Compile Include="Assets/Scripts/Screens/MainDashboard_Screen.cs" />
    <Compile Include="Assets/FishNet/Upgrading/EdgegapMenu.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/Scripts/Socket/TopBetData.cs" />
    <Compile Include="Assets/Scripts/Helper/WorldTimeAPI.cs" />
    <Compile Include="Assets/MetaMask/Scripts/IO/MetaMaskPlayerPrefsStorage.cs" />
    <Compile Include="Assets/HeadClamp.cs" />
    <Compile Include="Assets/Roulette Game Package/RouletteGame/Roulette/Scripts/Wheel.cs" />
    <Compile Include="Assets/Roulette Game Package/52SpecialEffectPack/EffectEditor/ShurikenEffectEditor/csShurikenEffectChanger.cs" />
    <Compile Include="Assets/Scripts/Screens/Advance_Screen.cs" />
    <Compile Include="Assets/MetaMask/Scripts/Contracts/ScriptableERC721.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Game/GridsLogic/GameManagerGridsShow.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Demo/Demo Scripts/UI/Shifters/MaterialShifter.cs" />
    <Compile Include="Assets/MetaMask/Samples/Main/Scripts/VisualController.cs" />
    <Compile Include="Assets/Scripts/KidsAppearancePanels/ModelOptionScript.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Game/Script/GameUtilities.cs" />
    <Compile Include="Assets/Scripts/GroupChatScript/GroupCommentPrefabScript.cs" />
    <Compile Include="Assets/Scenes/CameraFollow.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/Messages/ServerBinaryAckMessage.cs" />
    <Compile Include="Assets/TextMesh Pro/Examples &amp; Extras/Scripts/TMP_TextInfoDebugTool.cs" />
    <Compile Include="Assets/Black Jack Scene Package/Imported/BlackJack/Project/Multiplayer Blackjack/Blackjack Game/Scenes/Scripts/Blackjack_Card.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Game/Script/JsonEncryptionManager.cs" />
    <Compile Include="Assets/MetaMask/Samples/Main/Scripts/Tokens/NFTList.cs" />
    <Compile Include="Assets/Roulette Game Package/52SpecialEffectPack/EffectEditor/ShurikenEffectEditor/csShurikenEffectEditor.cs" />
    <Compile Include="Assets/SacredPlace/Misc/_DynamicClouds/cloud_behav.cs" />
    <Compile Include="Assets/Scripts/Others/Item_Invite_Friends.cs" />
    <Compile Include="Assets/Scripts/GroupChatScript/GroupChatWindowScript.cs" />
    <Compile Include="Assets/MetaMask/Scripts/IO/Infura.cs" />
    <Compile Include="Assets/Scripts/Screens/MainCharacter_Screen.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/JsonSerializer/SystemTextJsonSerializer.cs" />
    <Compile Include="Assets/MetaMask/Samples/Main/Scripts/CanvasScaler.cs" />
    <Compile Include="Assets/Samples/Universal RP/17.0.3/URP RenderGraph Samples/FramebufferFetch/FrameBufferFetchRenderFeature.cs" />
    <Compile Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/Black mage spells/Demo scene/Demo scene files/HS_CameraController.cs" />
    <Compile Include="Assets/ChipBettingManagement_Package/Scripts/ScriptableObjects/ChipConfig.cs" />
    <Compile Include="Assets/MetaMask/Samples/Main/Scripts/ModalPrefabData.cs" />
    <Compile Include="Assets/Roulette Game Package/RouletteGame/Roulette/Scripts/ObjectRotator.cs" />
    <Compile Include="Assets/Lets Make a VR Game/UI 3D Examples/UI_3D/Scripts/XRPushButton.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Game/GridsLogic/GridEditor.cs" />
    <Compile Include="Assets/MetaMask/Samples/Main/Scripts/Utils/ExtensionMethods.cs" />
    <Compile Include="Assets/MetaMask/Samples/Main/Scripts/Utils/Singleton.cs" />
    <Compile Include="Assets/VRBodyRotation.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/Transport/IHttpPollingHandler.cs" />
    <Compile Include="Assets/Brick Project Studio/_BPS Basic Assets/Common/Scripts and Animations/Drawer/Z Axis/Drawer_Pull_Z.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Scripts/Wearables/HiddenWearable.cs" />
    <Compile Include="Assets/Samples/Universal RP/17.0.3/URP Package Samples/RendererFeatures/DistortTunnel/DistortTunnelPass_Distort.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/Scene/SetionIdSc.cs" />
    <Compile Include="Assets/MetaMask/Samples/Main/Scripts/Tokens/TransferDialog.cs" />
    <Compile Include="Assets/PlayFlowCloud/Scripts/PlayFlowManager.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/Epic Toon FX Crash/Demo/Scripts/ETFXSceneManager.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Demo/Demo Scripts/UI/WearableDemoSceneUI.cs" />
    <Compile Include="Assets/CASINO/Scripts/Rotate.cs" />
    <Compile Include="Assets/Scripts/Enums/EnumCollection.cs" />
    <Compile Include="Assets/MetaMask/Samples/Main/Scripts/Visuals/TweenIn.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Demo/Demo Scripts/Editor Helpers/AddWearableHelper.cs" />
    <Compile Include="Assets/ChipBettingManagement_Package/Scripts/Managers/BetManager/ChipDistributer.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/Transport/TransportProtocol.cs" />
    <Compile Include="Assets/ToonKids/Scripts/TK3DController.cs" />
    <Compile Include="Assets/Scripts/Manager/AppManager.cs" />
    <Compile Include="Assets/Roulette Game Package/RouletteGame/Roulette/Scripts/RoomManager.cs" />
    <Compile Include="Assets/Samples/Universal RP/17.0.3/URP RenderGraph Samples/GbufferVisualization/GbufferVisualizationRendererFeature.cs" />
    <Compile Include="Assets/Brick Project Studio/_BPS Basic Assets/Common/Scripts and Animations/Doors/opencloseDoor.cs" />
    <Compile Include="Assets/TextMesh Pro/Examples &amp; Extras/Scripts/TMP_DigitValidator.cs" />
    <Compile Include="Assets/Scripts/Screens/Loading_Screen.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Scripts/Wearables/Wearable_Cosmetic.cs" />
    <Compile Include="Assets/StarterAssets/Mobile/Scripts/VirtualInputs/UIVirtualButton.cs" />
    <Compile Include="Assets/Scripts/Manager/SoundManager.cs" />
    <Compile Include="Assets/Scripts/GroupChatScript/GroupPostCommentsPanel.cs" />
    <Compile Include="Assets/TutorialInfo/Scripts/Readme.cs" />
    <Compile Include="Assets/VR_IK_ConstraintsManager.cs" />
    <Compile Include="Assets/CustomAvatarManagement.cs" />
    <Compile Include="Assets/VR Body/AnimateOnInput.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Scripts/Sets/CharacterMorph.cs" />
    <Compile Include="Assets/Scripts/Screens/CreateRoom_Screen.cs" />
    <Compile Include="Assets/TextMesh Pro/Examples &amp; Extras/Scripts/Benchmark01.cs" />
    <Compile Include="Assets/TextMesh Pro/Examples &amp; Extras/Scripts/TextMeshSpawner.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/Messages/PingMessage.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Scripts/Database/WearableDatabase.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Demo/Demo Scripts/UI/Elements/SlotReference.cs" />
    <Compile Include="Assets/SimpleJSON.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/Epic Toon FX Crash/Demo/Scripts/ETFXMouseOrbit.cs" />
    <Compile Include="Assets/Black Jack Scene Package/Imported/BlackJack/Project/Multiplayer Blackjack/Blackjack Game/Scenes/Scripts/Blackjack_GameManager.cs" />
    <Compile Include="Assets/GameSeatingManager.cs" />
    <Compile Include="Assets/Fantastic City Generator/Scripts/FreeCamera.cs" />
    <Compile Include="Assets/TextMesh Pro/Examples &amp; Extras/Scripts/CameraController.cs" />
    <Compile Include="Assets/Brick Project Studio/_BPS Basic Assets/Common/Scripts and Animations/First Person Player/PlayerMovement.cs" />
    <Compile Include="Assets/Scripts/Roulette Wheel Modern.cs" />
    <Compile Include="Assets/GPGSIds.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Game/Script/Roles.cs" />
    <Compile Include="Assets/Scripts/Manager/TouchInputManager.cs" />
    <Compile Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/Black mage spells/Scripts/ParticleCollisionInstance.cs" />
    <Compile Include="Assets/TextMesh Pro/Examples &amp; Extras/Scripts/Benchmark03.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/WebSocket/WebSocket.cs" />
    <Compile Include="Assets/Roulette Game Package/RouletteGame/Roulette/Scripts/BetSpace.cs" />
    <Compile Include="Assets/Scripts/Screens/SubScreens/Request_Subscreen.cs" />
    <Compile Include="Assets/Scripts/Screens/ErrorMessage_Screen.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/Scripts/UserData/UserActualData.cs" />
    <Compile Include="Assets/ChipBettingManagement_Package/Scripts/Chip/ChipInteractor.cs" />
    <Compile Include="Assets/Scripts/GroupChat/Scripts/UIScripts/UIManager.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOCouroutineRunner.cs" />
    <Compile Include="Assets/Scripts/Screens/SubScreens/GroupSubScreen.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Demo/Demo Scripts/UI/Elements/MorphSlider.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/Messages/ClientAckMessage.cs" />
    <Compile Include="Assets/Scripts/Others/Request.cs" />
    <Compile Include="Assets/MetaMask/Scripts/Cryptography/BouncyEciesProvider.cs" />
    <Compile Include="Assets/Fantastic City Generator/Scripts/TrafficLights2.cs" />
    <Compile Include="Assets/Fantastic City Generator/Water/WaterSample.cs" />
    <Compile Include="Assets/MetaMask/Samples/Main/Scripts/Tokens/NFTPanel.cs" />
    <Compile Include="Assets/Scripts/Handler/FacebookHandler.cs" />
    <Compile Include="Assets/ChipBettingManagement_Package/Scripts/ChipsPool/ChipPoolManager.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Plugins/JSON/JSONObject.cs" />
    <Compile Include="Assets/Samples/Universal RP/17.0.3/URP Package Samples/RendererFeatures/DistortTunnel/DistortTunnelPass_Tunnel.cs" />
    <Compile Include="Assets/Scripts/GroupChatScript/CreateGroupPostScript.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Scripts/General/WearableSystemEnums.cs" />
    <Compile Include="Assets/Scripts/BlockchainManager/ContractDataSO.cs" />
    <Compile Include="Assets/Roulette Game Package/RouletteGame/Roulette/Scripts/Cloth.cs" />
    <Compile Include="Assets/Samples/Universal RP/17.0.3/URP Package Samples/RendererFeatures/DepthBlit/DepthBlitFeature.cs" />
    <Compile Include="Assets/Fantastic City Generator/Scripts/TrafficLight.cs" />
    <Compile Include="Assets/MetaMask/Scripts/Transports/Unity/MetaMaskUnityTransportBroadcaster.cs" />
    <Compile Include="Assets/TextMesh Pro/Examples &amp; Extras/Scripts/DropdownSample.cs" />
    <Compile Include="Assets/VR Body/IKTargetFollowVRRig.cs" />
    <Compile Include="Assets/TextMesh Pro/Examples &amp; Extras/Scripts/VertexColorCycler.cs" />
    <Compile Include="Assets/Roulette Game Package/RouletteGame/Roulette/Scripts/WinSequence.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Game/Shiny Effect/Demo_ShinyEffectForUGUI.cs" />
    <Compile Include="Assets/Scripts/VRPlayer_WatchMenu.cs" />
    <Compile Include="Assets/MetaMask/Samples/Main/Scripts/ModalData.cs" />
    <Compile Include="Assets/Roulette Game Package/RouletteGame/Roulette/Scripts/SplitBetGroup.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Game/Shiny Effect/ShinyEffectForUGUI.cs" />
    <Compile Include="Assets/Brick Project Studio/Apartment Kit/Common/Scripts &amp; Animation/TableFlip/TableFlipL.cs" />
    <Compile Include="Assets/ChipBettingManagement_Package/Scripts/ScriptableObjects/ChipPoolData.cs" />
    <Compile Include="Assets/Scripts/Screens/ForgetPasswordScreen.cs" />
    <Compile Include="Assets/Samples/Universal RP/17.0.3/URP Package Samples/SharedAssets/Scripts/FirstPersonController.cs" />
    <Compile Include="Assets/Scripts/Others/Item_UI_Search.cs" />
    <Compile Include="Assets/Lets Make a VR Game/UI 3D Examples/UI_3D/Scripts/XRSlider.cs" />
    <Compile Include="Assets/Scripts/GroupChatScript/GroupInvitesWindow.cs" />
    <Compile Include="Assets/MetaMask/Samples/Main/Scripts/Utils/Images/ImageDownloadRequest.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/Scripts/UserData/User Message Script.cs" />
    <Compile Include="Assets/Black Jack Scene Package/Imported/BlackJack/Project/Multiplayer Blackjack/Blackjack Game/Scenes/Scripts/Blackjack_BetableSpace.cs" />
    <Compile Include="Assets/TextMesh Pro/Examples &amp; Extras/Scripts/TMP_PhoneNumberValidator.cs" />
    <Compile Include="Assets/Roulette Game Package/RouletteGame/Roulette/Scripts/AmericanWheel.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/Scripts/Socket/Cappadocia_SessionIdExtractor.cs" />
    <Compile Include="Assets/Scripts/KidsAppearancePanels/ModelsPanel.cs" />
    <Compile Include="Assets/Scripts/PlayAudioFromAudioManager.cs" />
    <Compile Include="Assets/Samples/Universal RP/17.0.3/URP RenderGraph Samples/TextureReference w. FrameData/TextureRefRendererFeature.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Demo/Demo Scripts/UI/ClosetDemoUI.cs" />
    <Compile Include="Assets/Samples/Universal RP/17.0.3/URP RenderGraph Samples/Blit/CopyRenderFeature.cs" />
    <Compile Include="Assets/Brick Project Studio/Apartment Kit/Common/Scripts &amp; Animation/Closet/ClosetopencloseDoor.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Scripts/Styles/StyleMaterial.cs" />
    <Compile Include="Assets/ChipBettingManagement_Package/Scripts/Managers/CetralizedManager/ChipManager.cs" />
    <Compile Include="Assets/Samples/Universal RP/17.0.3/URP RenderGraph Samples/BlitWithMaterial/BlitAndSwapColorRendererFeature.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/Messages/ServerAckMessage.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Demo/Demo Scripts/UI/Shifters/WearableShifter.cs" />
    <Compile Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Game/Script/HomeManager.cs" />
    <Compile Include="Assets/Black Jack Scene Package/Imported/BlackJack/Project/Multiplayer Blackjack/Blackjack Game/Scenes/Scripts/DealerAnimationHandler.cs" />
    <Compile Include="Assets/Samples/Universal RP/17.0.3/URP RenderGraph Samples/OutputTexture/OutputTextureRendererFeature.cs" />
    <Compile Include="Assets/MetaMask/Samples/Main/Scripts/Tokens/Token.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Scripts/Modular System/CleanManager.cs" />
    <Compile Include="Assets/Scene Teleportation Kit/Scripts/player/PlayerMovement.cs" />
    <Compile Include="Assets/Scripts/Screens/Furnish_Screen.cs" />
    <Compile Include="Assets/TextMesh Pro/Examples &amp; Extras/Scripts/TextMeshProFloatingText.cs" />
    <Compile Include="Assets/Fantastic City Generator/Player/Player Mobile/MobileControl.cs" />
    <Compile Include="Assets/StarterAssets/ThirdPersonController/Scripts/BasicRigidBodyPush.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/Transport/IClientWebSocket.cs" />
    <Compile Include="Assets/Scripts/PlayerManagers/PlayerMovemetManager.cs" />
    <Compile Include="Assets/ConsoleToText.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Scripts/Sets/WearableSetObject.cs" />
    <Compile Include="Assets/ToonKids/Scripts/TKGirlPrefabMaker_VR.cs" />
    <Compile Include="Assets/Scripts/Managers/ProfileManager.cs" />
    <Compile Include="Assets/Scripts/BlockchainManager/MetamaskManager.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/Epic Toon FX Crash/Scripts/ETFXLightFade.cs" />
    <Compile Include="Assets/Scripts/Screens/SignIn_Screen.cs" />
    <Compile Include="Assets/Scripts/Others/ZoomScript.cs" />
    <Compile Include="Assets/ChipBettingManagement_Package/Scripts/Managers/ChipStackManager/ChipStack.cs" />
    <Compile Include="Assets/Stellar Game Assets/Modular Character System/Scripts/Wearables/Wearable_Clothing.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/Scripts/UserData/UserTopData.cs" />
    <Compile Include="Assets/SacredPlace/Misc/Scripts/FPCharacter.cs" />
    <Compile Include="Assets/Scripts/GroupChatScript/GroupMessagePrefabScript.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/Luke Peek/Realistic Fire/Scripts/LightFlicker.cs" />
    <Compile Include="Assets/Scripts/SetTurnTypeFromPlayerPref.cs" />
    <Compile Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/SocketIOClient/Messages/MessageType.cs" />
    <Compile Include="Assets/Slot Games Assets/King Of Cappadocia/Aditya/Chat System/Message Resources/ChatUIManager.cs" />
    <Compile Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/Toon Projectiles 2/Scripts/AutoDestroyPS.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Assets/TextMesh Pro/Shaders/TMPro.cginc" />
    <None Include="Assets/SacredPlace/_Built-in Pipeline/IMPORTANT.txt" />
    <None Include="Assets/Slot Games Assets/King Of Cappadocia/Epic Toon FX Crash/Prefabs/Environment/Environment FX.txt" />
    <None Include="Assets/MetaMask/Plugins/Libraries/evm.net/Plugins/netstandard2.1/Nethereum.Util.dll" />
    <None Include="Assets/TextMesh Pro/Shaders/TMP_SDF-Mobile Overlay.shader" />
    <None Include="Assets/Samples/Universal RP/17.0.3/URP RenderGraph Samples/Compute/ComputeShaderExample.compute" />
    <None Include="Assets/TextMesh Pro/Examples &amp; Extras/Fonts/Roboto-Bold - License.txt" />
    <None Include="Assets/MetaMask/Plugins/Libraries/evm.net/Plugins/netstandard2.1/Nethereum.ABI.dll" />
    <None Include="Assets/TextMesh Pro/Shaders/TMP_SDF-Mobile-2-Pass.shader" />
    <None Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/BuildReport/CustomBuildScriptExample.txt" />
    <None Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/Sword slash VFX/Demo scene/HDRP and URP.txt" />
    <None Include="Assets/MetaMask/Plugins/Libraries/evm.net/Plugins/netstandard2.0/Nethereum.RLP.dll" />
    <None Include="Assets/Slot Games Assets/King Of Cappadocia/Epic Toon FX Crash/Prefabs/Combat/Combat FX.txt" />
    <None Include="Assets/Samples/Universal RP/17.0.3/URP RenderGraph Samples/GbufferVisualization/GBuffer_Visualization_Shader_Sample.shader" />
    <None Include="Assets/TextMesh Pro/Examples &amp; Extras/Fonts/Unity - OFL.txt" />
    <None Include="Assets/TextMesh Pro/Shaders/TMP_Bitmap.shader" />
    <None Include="Assets/Slot Games Assets/King Of Cappadocia/Epic Toon FX Crash/Changelog.txt" />
    <None Include="Assets/MetaMask/Plugins/Libraries/zxing.unity.dll" />
    <None Include="Assets/TextMesh Pro/Examples &amp; Extras/Fonts/Oswald-Bold - OFL.txt" />
    <None Include="Assets/MetaMask/Plugins/Libraries/evm.net/Plugins/netstandard2.0/ImpromptuInterface.dll" />
    <None Include="Assets/MetaMask/Plugins/Libraries/evm.net/Runtime/netstandard2.1/evm.net.dll" />
    <None Include="Assets/TextMesh Pro/Examples &amp; Extras/Fonts/Roboto-Bold - AFL.txt" />
    <None Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/Black mage spells/Shaders/Lit_CenterGlow.shader" />
    <None Include="Assets/TextMesh Pro/Examples &amp; Extras/Fonts/Anton OFL.txt" />
    <None Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/Toon Projectiles 2/Demo scene/Readme.txt" />
    <None Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/Black mage spells/Shaders/SmoothSmoke.shader" />
    <None Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/Game/Shiny Effect/UI-Effect-Shiny.shader" />
    <None Include="Assets/FishNet/LICENSE.txt" />
    <None Include="Assets/CASINO/Shaders/sh_unlit_anim_01.shader" />
    <None Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/NativeCursor/Cursors/MacOS/license.txt" />
    <None Include="Assets/LasVegas_Package_Export/PalmTreePack/Shaders/Readme.txt" />
    <None Include="Assets/TextMesh Pro/Shaders/TMPro_Mobile.cginc" />
    <None Include="Assets/SacredPlace/Misc/_DynamicClouds/ReadMe.txt" />
    <None Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/Sword slash VFX/Shaders/DissolveNoise.shader" />
    <None Include="Assets/TextMesh Pro/Shaders/TMP_SDF SSD.shader" />
    <None Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/libs/Microsoft.Bcl.AsyncInterfaces.dll" />
    <None Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/NativeCursor/Cursors/Borealis/license.txt" />
    <None Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/BuildReport/README.txt" />
    <None Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/libs/System.Buffers.dll" />
    <None Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/Black mage spells/Demo scene/About demo.txt" />
    <None Include="Assets/MetaMask/Plugins/Libraries/evm.net/Plugins/netstandard2.1/EventEmitter.NET.dll" />
    <None Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/Sword slash VFX/Shaders/Add_Fresnel.shader" />
    <None Include="Assets/PlayFlowCloud/Lib/LICENSE.txt" />
    <None Include="Assets/TextMesh Pro/Shaders/TMP_SDF-Surface-Mobile.shader" />
    <None Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/libs/System.Reactive.dll" />
    <None Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/Sword slash VFX/Shaders/Ice.shader" />
    <None Include="Assets/MetaMask/Runtime/netstandard2.0/MetaMask.SDK.dll" />
    <None Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/Sword slash VFX/Shaders/Distortion.shader" />
    <None Include="Assets/MetaMask/Plugins/Libraries/evm.net/Plugins/netstandard2.1/Dynamitey.dll" />
    <None Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/Sword slash VFX/Shaders/Blend_TwoSides.shader" />
    <None Include="Assets/MetaMask/Plugins/Libraries/evm.net/Plugins/netstandard2.0/Nethereum.Util.dll" />
    <None Include="Assets/TextMesh Pro/Shaders/SDFFunctions.hlsl" />
    <None Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/NativeCursor/Cursors/GoogleDot-Blue-Regular/license.txt" />
    <None Include="Assets/MetaMask/Plugins/Libraries/evm.net/Plugins/netstandard2.0/Nethereum.ABI.dll" />
    <None Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/Sword slash VFX/Shaders/Blend_CenterGlow.shader" />
    <None Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/NativeCursor/Cursors/Nordzy/license.txt" />
    <None Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/NativeCursor/Cursors/Linux/readme.txt" />
    <None Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/libs/System.Text.Encodings.Web.dll" />
    <None Include="Assets/MetaMask/Runtime/netstandard2.1/MetaMask.SDK.dll" />
    <None Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/Render Pipelines support/Documentation.txt" />
    <None Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/Sword slash VFX/Demo scene/Readme.txt" />
    <None Include="Assets/Roulette Game Package/RouletteGame/Roulette/Third-Party Notices.txt" />
    <None Include="Assets/TextMesh Pro/Shaders/TMP_SDF-Surface.shader" />
    <None Include="Assets/MetaMask/Plugins/Libraries/evm.net/Plugins/netstandard2.0/Dynamitey.dll" />
    <None Include="Assets/Slot Games Assets/King Of Cappadocia/ShinyEffectForUGUI-develop/LICENSE.txt" />
    <None Include="Assets/Slot Games Assets/King Of Cappadocia/Epic Toon FX Crash/Prefabs/Interactive/Interactive FX.txt" />
    <None Include="Assets/Samples/Universal RP/17.0.3/URP RenderGraph Samples/MRT/MrtColor.shader" />
    <None Include="Assets/TextMesh Pro/Sprites/EmojiOne Attribution.txt" />
    <None Include="Assets/Samples/Universal RP/17.0.3/URP RenderGraph Samples/BlitWithMaterial/BlitWithMaterial.shader" />
    <None Include="Assets/TextMesh Pro/Shaders/TMP_Bitmap-Custom-Atlas.shader" />
    <None Include="Assets/TextMesh Pro/Shaders/TMP_SDF.shader" />
    <None Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/BuildReport/license.txt" />
    <None Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/PBR Fantasy staff/Shaders/Readme.txt" />
    <None Include="Assets/Lets Make a VR Game/ReadMe.txt" />
    <None Include="Assets/StarterAssets/license.txt" />
    <None Include="Assets/FishNet/DOCUMENTATION.txt" />
    <None Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/Black mage spells/Shaders/Lightning.shader" />
    <None Include="Assets/TextMesh Pro/Shaders/TMP_SDF-Mobile SSD.shader" />
    <None Include="Assets/TextMesh Pro/Resources/LineBreaking Leading Characters.txt" />
    <None Include="Assets/PlayFlowCloud/Lib/ICSharpCode.SharpZipLib.xml" />
    <None Include="Assets/Scripts/GradientSkybox.shader" />
    <None Include="Assets/MetaMask/Plugins/Libraries/BouncyCastle.Crypto.dll" />
    <None Include="Assets/PlayFlowCloud/README.txt" />
    <None Include="Assets/TextMesh Pro/Shaders/TMPro_Properties.cginc" />
    <None Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/PBR Fantasy staff/Shaders/Staff.shader" />
    <None Include="Assets/MetaMask/Plugins/Libraries/evm.net/Plugins/netstandard2.0/Nethereum.Hex.dll" />
    <None Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/libs/System.Threading.Tasks.Extensions.dll" />
    <None Include="Assets/MetaMask/Plugins/Libraries/evm.net/Runtime/netstandard2.0/evm.net.dll" />
    <None Include="Assets/Slot Games Assets/King Of Cappadocia/_GameAssets/BuildReport/VERSION.txt" />
    <None Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/libs/System.Runtime.CompilerServices.Unsafe.dll" />
    <None Include="Assets/Slot Games Assets/King Of Cappadocia/Epic Toon FX Crash/Upgrade/URP Compatibility.txt" />
    <None Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/Black mage spells/Shaders/Blend_LinePath.shader" />
    <None Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/Toon Projectiles 2/Demo scene/HDRP and URP(LWRP).txt" />
    <None Include="Assets/TextMesh Pro/Shaders/TMPro_Surface.cginc" />
    <None Include="Assets/SacredPlace/_HDRP Pipeline/USAGE.txt" />
    <None Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/NativeCursor/Cursors/BreezeX-Dark/license.txt" />
    <None Include="Assets/LasVegas_Package_Export/PalmTreePack/Doc/QuickStart.txt" />
    <None Include="Assets/SacredPlace/Misc/_DynamicClouds/PoTCloud.shader" />
    <None Include="Assets/Samples/Universal RP/17.0.3/URP Package Samples/SharedAssets/Shaders/3DText.shader" />
    <None Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/Sword slash VFX/Shaders/BlendDistort.shader" />
    <None Include="Assets/TextMesh Pro/Resources/LineBreaking Following Characters.txt" />
    <None Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/Black mage spells/Shaders/AddTrail.shader" />
    <None Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/Sword slash VFX/Shaders/Scroll.shader" />
    <None Include="Assets/MetaMask/Plugins/Libraries/evm.net/Plugins/netstandard2.1/Nethereum.Hex.dll" />
    <None Include="Assets/PlayFlowCloud/Documentation/Documentation.txt" />
    <None Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/Black mage spells/Shaders/Fire.shader" />
    <None Include="Assets/MetaMask/Plugins/Libraries/evm.net/Plugins/netstandard2.1/ImpromptuInterface.dll" />
    <None Include="Assets/SceneSwitcherPro/Readme.txt" />
    <None Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/libs/System.Text.Json.dll" />
    <None Include="Assets/Wristwatch/Shaders/AO.shader" />
    <None Include="Assets/Slot Games Assets/King Of Cappadocia/ShinyEffectForUGUI-develop/Assets/ShinyEffectForUGUI/LICENSE.txt" />
    <None Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/NativeCursor/Cursors/Bibata-Modern-Ice-Right/license.txt" />
    <None Include="Assets/LasVegas_Package_Export/Assets/NewSurfaceShader.shader" />
    <None Include="Assets/PlayFlowCloud/Lib/ICSharpCode.SharpZipLib.dll" />
    <None Include="Assets/TextMesh Pro/Shaders/TMP_SDF-Mobile Masking.shader" />
    <None Include="Assets/SacredPlace/ReadMe.txt" />
    <None Include="Assets/VisualX_Studio/Sci-Fi_Loading_Screen_Effects_FREE/VisualX_Sci-Fi Loading Screen Effects_FREE.txt" />
    <None Include="Assets/LasVegas_Package_Export/PalmTreePack/Models/Palm_Style_Old/Readme.txt" />
    <None Include="Assets/Samples/Universal RP/17.0.3/URP RenderGraph Samples/FramebufferFetch/FrameBufferFetch.shader" />
    <None Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/Sword slash VFX/Shaders/Add_CenterGlow.shader" />
    <None Include="Assets/TextMesh Pro/Shaders/TMP_SDF Overlay.shader" />
    <None Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/8 KI levels/Demo scene/HDRP and URP.txt" />
    <None Include="Assets/TextMesh Pro/Shaders/TMP_Bitmap-Mobile.shader" />
    <None Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/NativeCursor/Plugins/0Harmony.dll" />
    <None Include="Assets/TextMesh Pro/Fonts/LiberationSans - OFL.txt" />
    <None Include="Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/libs/System.Memory.dll" />
    <None Include="Assets/MetaMask/Plugins/Libraries/evm.net/Plugins/netstandard2.0/EventEmitter.NET.dll" />
    <None Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/Black mage spells/Shaders/ShockWave.shader" />
    <None Include="Assets/TextMesh Pro/Shaders/TMP_SDF-Mobile.shader" />
    <None Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/Magic effects pack/Demo scene/Readme.txt" />
    <None Include="Assets/MetaMask/Plugins/Libraries/evm.net/Plugins/netstandard2.1/Nethereum.RLP.dll" />
    <None Include="Assets/Slot Games Assets/King Of Cappadocia/ShinyEffectForUGUI-develop/ProjectSettings/ProjectVersion.txt" />
    <None Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/Sword slash VFX/Shaders/Explosion.shader" />
    <None Include="Assets/TextMesh Pro/Shaders/TMP_Sprite.shader" />
    <None Include="Assets/Roulette Game Package/ParticlesPackages/Hovl Studio/Sword slash VFX/Shaders/SwordSlash.shader" />
    <None Include="Assets/TextMesh Pro/Examples &amp; Extras/Fonts/Bangers - OFL.txt" />
    <None Include="Assets/Slot Games Assets/Egyption Treasures Slot Game/NativeCursor/Plugins/LICENSE.txt" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ARModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AnimationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.AudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ClothModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ContentLoadModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.DirectorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GameCenterModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GraphicsStateCollectionSerializerModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HierarchyCoreModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.HotReloadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.IMGUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputForUIModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputForUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.LocalizationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.MarshallingModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MarshallingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.MultiplayerModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PropertiesModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ShaderVariantAnalyticsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.StreamingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TLSModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VehiclesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.WindModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEngine.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AccessibilityModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.AdaptivePerformanceModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.BuildProfileModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreBusinessMetricsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EditorToolbarModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EmbreeModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.EmbreeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GIModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphicsStateCollectionSerializerModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GridAndSnapModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GridModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.MultiplayerModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Physics2DModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PhysicsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PresetsUIModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PropertiesModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SafeModeModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SafeModeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneViewModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SceneViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.ShaderFoundryModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SketchUpModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SketchUpModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpriteMaskModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpriteShapeModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SubstanceModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TerrainModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextRenderingModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TilemapModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TreeModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.TreeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIAutomationModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UmbraModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VFXModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.VideoModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.XRModule">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/Managed/UnityEngine/UnityEditor.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.RPC">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.RPC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Util">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Util.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.ABI">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.ABI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.RLP">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.RLP.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Mud.Contracts">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Mud.Contracts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Geth">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Geth.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Optimism">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Optimism.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.BlockchainProcessing">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.BlockchainProcessing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections.LowLevel.ILSupport">
      <HintPath>Library/PackageCache/com.unity.collections/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Model">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Model.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="nunit.framework">
      <HintPath>Library/PackageCache/com.unity.ext.nunit/net40/unity-custom/nunit.framework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="FishNet.CodeAnalysis">
      <HintPath>Assets/FishNet/Runtime/Plugins/CodeAnalysis/FishNet.CodeAnalysis.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="zxing.unity">
      <HintPath>Assets/MetaMask/Plugins/Libraries/zxing.unity.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Signer">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Signer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="ImpromptuInterface">
      <HintPath>Assets/MetaMask/Plugins/Libraries/evm.net/Plugins/netstandard2.1/ImpromptuInterface.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="evm.net">
      <HintPath>Assets/MetaMask/Plugins/Libraries/evm.net/Runtime/netstandard2.1/evm.net.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Siwe.Core">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Siwe.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="ADRaffy.ENSNormalize">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/ADRaffy.ENSNormalize.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Merkle.Patricia">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Merkle.Patricia.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces">
      <HintPath>Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/libs/Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DemiLib">
      <HintPath>Assets/Plugins/Demigiant/DemiLib/Core/DemiLib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="EventEmitter.NET">
      <HintPath>Assets/MetaMask/Plugins/Libraries/evm.net/Plugins/netstandard2.1/EventEmitter.NET.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.JsonRpc.RpcClient">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.JsonRpc.RpcClient.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reactive">
      <HintPath>Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/libs/System.Reactive.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="BouncyCastle.Crypto">
      <HintPath>Assets/MetaMask/Plugins/Libraries/BouncyCastle.Crypto.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.HdWallet">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.HdWallet.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="MetaMask.SDK">
      <HintPath>Assets/MetaMask/Runtime/netstandard2.1/MetaMask.SDK.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Dynamitey">
      <HintPath>Assets/MetaMask/Plugins/Libraries/evm.net/Plugins/netstandard2.1/Dynamitey.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.EVM">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.EVM.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Web3">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Web3.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.UI">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Metamask">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Metamask.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Unity">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Unity.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encodings.Web">
      <HintPath>Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/libs/System.Text.Encodings.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Hex">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Hex.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.JsonRpc.Client">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.JsonRpc.Client.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Accounts">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Accounts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTween">
      <HintPath>Assets/Plugins/Demigiant/DOTween/DOTween.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Unity.Metamask">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Unity.Metamask.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe">
      <HintPath>Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/libs/System.Runtime.CompilerServices.Unsafe.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Besu">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Besu.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Antlr3.Runtime">
      <HintPath>Library/PackageCache/com.unity.visualscripting/Runtime/VisualScripting.Flow/Dependencies/NCalc/Unity.VisualScripting.Antlr3.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTweenPro">
      <HintPath>Assets/Plugins/Demigiant/DOTweenPro/DOTweenPro.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Merkle">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Merkle.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Signer.EIP712">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Signer.EIP712.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.GnosisSafe">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.GnosisSafe.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Json">
      <HintPath>Assets/MetaMask/Plugins/Libraries/SocketIOUnity/Runtime/libs/System.Text.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>Library/PackageCache/com.unity.nuget.newtonsoft-json/Runtime/Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Mono.Cecil">
      <HintPath>Library/PackageCache/com.unity.nuget.mono-cecil/Mono.Cecil.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib">
      <HintPath>Assets/PlayFlowCloud/Lib/ICSharpCode.SharpZipLib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Mud">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Mud.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Siwe">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Siwe.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Util.Rest">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Util.Rest.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="NBitcoin">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/NBitcoin.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.KeyStore">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.KeyStore.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Nethereum.Contracts">
      <HintPath>Library/PackageCache/com.nethereum.unity/Runtime/Nethereum.Contracts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.Types">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/PlaybackEngines/AndroidPlayer/Unity.Android.Types.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.Gradle">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/PlaybackEngines/AndroidPlayer/Unity.Android.Gradle.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS.Extensions.Xcode">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.iOS.Extensions.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/ref/2.1.0/netstandard.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tracing">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Web">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Web">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Windows">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Serialization">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/System.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>/Applications/Unity/Hub/Editor/6000.0.25f1/Unity.app/Contents/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.Interaction.Toolkit.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.XR.Interaction.Toolkit.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.CoreUtils">
      <HintPath>Library/ScriptAssemblies/Unity.XR.CoreUtils.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="PsdPlugin">
      <HintPath>Library/ScriptAssemblies/PsdPlugin.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Common.Path.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.2D.Common.Path.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Toolchain.Macos-arm64-Linux-x86_64">
      <HintPath>Library/ScriptAssemblies/Unity.Toolchain.Macos-arm64-Linux-x86_64.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.IK.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.2D.IK.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="glTFast.dots">
      <HintPath>Library/ScriptAssemblies/glTFast.dots.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.IK.Runtime">
      <HintPath>Library/ScriptAssemblies/Unity.2D.IK.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.ShaderGraph.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.ShaderGraph.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Timeline">
      <HintPath>Library/ScriptAssemblies/Unity.Timeline.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.ShaderLibrary">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Aseprite.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.2D.Aseprite.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Multiplayer.Center.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.GPUDriven.Runtime">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.GPUDriven.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Mobile.AndroidLogcat.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.Mobile.AndroidLogcat.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Avaturn.Core.Runtime">
      <HintPath>Library/ScriptAssemblies/Avaturn.Core.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Autodesk.Fbx">
      <HintPath>Library/ScriptAssemblies/Autodesk.Fbx.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Bindings.OpenImageIO.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.Bindings.OpenImageIO.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.OpenXR.Features.OculusQuestSupport.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.XR.OpenXR.Features.OculusQuestSupport.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="PPv2URPConverters">
      <HintPath>Library/ScriptAssemblies/PPv2URPConverters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Recorder.Base">
      <HintPath>Library/ScriptAssemblies/Unity.Recorder.Base.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Core">
      <HintPath>Library/ScriptAssemblies/Unity.VisualScripting.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.InternalAPIEngineBridge.001">
      <HintPath>Library/ScriptAssemblies/Unity.InternalAPIEngineBridge.001.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.Hands.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.XR.Hands.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Tilemap.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.2D.Tilemap.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Config.Runtime">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Config.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Aseprite.Common">
      <HintPath>Library/ScriptAssemblies/Unity.2D.Aseprite.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Animation.Runtime">
      <HintPath>Library/ScriptAssemblies/Unity.2D.Animation.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.Collections.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Shared.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Core.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpatialTracking">
      <HintPath>Library/ScriptAssemblies/UnityEngine.SpatialTracking.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.Hands">
      <HintPath>Library/ScriptAssemblies/Unity.XR.Hands.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Animation.Rigging">
      <HintPath>Library/ScriptAssemblies/Unity.Animation.Rigging.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Autodesk.Fbx.Editor">
      <HintPath>Library/ScriptAssemblies/Autodesk.Fbx.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.Management">
      <HintPath>Library/ScriptAssemblies/Unity.XR.Management.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.TextMeshPro">
      <HintPath>Library/ScriptAssemblies/Unity.TextMeshPro.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.PlasticSCM.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.PlasticSCM.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Tilemap.Extras">
      <HintPath>Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.SettingsProvider.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.OpenXR.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.XR.OpenXR.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Runtime">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.State">
      <HintPath>Library/ScriptAssemblies/Unity.VisualScripting.State.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Sprite.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.2D.Sprite.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UniWebView-CSharp.Editor">
      <HintPath>Library/ScriptAssemblies/UniWebView-CSharp.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Psdimporter.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.2D.Psdimporter.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Recorder.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.Recorder.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.OpenXR.Features.MetaQuestSupport.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.XR.OpenXR.Features.MetaQuestSupport.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.OpenXR">
      <HintPath>Library/ScriptAssemblies/Unity.XR.OpenXR.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Runtime">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="glTFast.Editor">
      <HintPath>Library/ScriptAssemblies/glTFast.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Flow">
      <HintPath>Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.CoreUtils.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.XR.CoreUtils.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SpatialTracking">
      <HintPath>Library/ScriptAssemblies/UnityEditor.SpatialTracking.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.SysrootPackage.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.SysrootPackage.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.2D.Runtime">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="com.unity.cinemachine.editor">
      <HintPath>Library/ScriptAssemblies/com.unity.cinemachine.editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.InputSystem.ForUI">
      <HintPath>Library/ScriptAssemblies/Unity.InputSystem.ForUI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UI">
      <HintPath>Library/ScriptAssemblies/UnityEditor.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Searcher.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.Searcher.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Animation.Rigging.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.Animation.Rigging.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Rider.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.Rider.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.OpenXR.Features.ConformanceAutomation">
      <HintPath>Library/ScriptAssemblies/Unity.XR.OpenXR.Features.ConformanceAutomation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.OpenXR.Features.OculusQuestSupport">
      <HintPath>Library/ScriptAssemblies/Unity.XR.OpenXR.Features.OculusQuestSupport.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Common.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.2D.Common.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Recorder">
      <HintPath>Library/ScriptAssemblies/Unity.Recorder.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipeline.Universal.ShaderLibrary">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Universal.Shaders">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.SpriteShape.Runtime">
      <HintPath>Library/ScriptAssemblies/Unity.2D.SpriteShape.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Runtime.Shared">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.Core.Runtime.Shared.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Timeline.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.Timeline.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Editor.Shared">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.Shared.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="glTFast">
      <HintPath>Library/ScriptAssemblies/glTFast.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UI">
      <HintPath>Library/ScriptAssemblies/UnityEngine.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Mathematics.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.Mathematics.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.XR.LegacyInputHelpers">
      <HintPath>Library/ScriptAssemblies/UnityEditor.XR.LegacyInputHelpers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.InternalAPIEditorBridge.001">
      <HintPath>Library/ScriptAssemblies/Unity.InternalAPIEditorBridge.001.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Animation.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.2D.Animation.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections">
      <HintPath>Library/ScriptAssemblies/Unity.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.PixelPerfect.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.2D.PixelPerfect.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Rendering.LightTransport.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.Rendering.LightTransport.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.Management.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.XR.Management.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.XR.LegacyInputHelpers">
      <HintPath>Library/ScriptAssemblies/UnityEngine.XR.LegacyInputHelpers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UniWebView-CSharp">
      <HintPath>Library/ScriptAssemblies/UniWebView-CSharp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.OpenXR.Features.RuntimeDebugger.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.XR.OpenXR.Features.RuntimeDebugger.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Tilemap.Extras.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Multiplayer.Center.Common">
      <HintPath>Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.Flow.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.Interaction.Toolkit">
      <HintPath>Library/ScriptAssemblies/Unity.XR.Interaction.Toolkit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.Common.Runtime">
      <HintPath>Library/ScriptAssemblies/Unity.2D.Common.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualScripting.State.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.VisualScripting.State.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Mathematics">
      <HintPath>Library/ScriptAssemblies/Unity.Mathematics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Cinemachine">
      <HintPath>Library/ScriptAssemblies/Cinemachine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.RenderPipelines.Core.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.XR.OpenXR.Features.MetaQuestSupport">
      <HintPath>Library/ScriptAssemblies/Unity.XR.OpenXR.Features.MetaQuestSupport.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VSCode.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.VSCode.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.SpriteShape.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.2D.SpriteShape.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Formats.Fbx.Runtime">
      <HintPath>Library/ScriptAssemblies/Unity.Formats.Fbx.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Sysroot.Linux_x86_64">
      <HintPath>Library/ScriptAssemblies/Unity.Sysroot.Linux_x86_64.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.TextMeshPro.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.TextMeshPro.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Burst.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.Burst.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.2D.PixelPerfect">
      <HintPath>Library/ScriptAssemblies/Unity.2D.PixelPerfect.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Formats.Fbx.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.Formats.Fbx.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Burst">
      <HintPath>Library/ScriptAssemblies/Unity.Burst.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.VisualStudio.Editor">
      <HintPath>Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Rendering.LightTransport.Runtime">
      <HintPath>Library/ScriptAssemblies/Unity.Rendering.LightTransport.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.InputSystem">
      <HintPath>Library/ScriptAssemblies/Unity.InputSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="Assembly-CSharp-firstpass.csproj" />
    <ProjectReference Include="com.bennykok.runtime-debug-action.csproj" />
    <ProjectReference Include="Unity.XR.Interaction.Toolkit.Samples.SpatialKeyboard.Editor.csproj" />
    <ProjectReference Include="Unity.XR.Interaction.Toolkit.Samples.StarterAssets.csproj" />
    <ProjectReference Include="BestHTTP.csproj" />
    <ProjectReference Include="IngameDebugConsole.Editor.csproj" />
    <ProjectReference Include="Unity.XR.Hands.Samples.VisualizerSample.csproj" />
    <ProjectReference Include="BuildReportTool.csproj" />
    <ProjectReference Include="FishNet.Runtime.csproj" />
    <ProjectReference Include="FishNet.Codegen.Cecil.csproj" />
    <ProjectReference Include="FishNet.Demos.csproj" />
    <ProjectReference Include="GameKit.Dependencies.csproj" />
    <ProjectReference Include="NativeCursor.Runtime.csproj" />
    <ProjectReference Include="Webview.Editor.csproj" />
    <ProjectReference Include="Unity.XR.Interaction.Toolkit.Samples.Hands.Editor.csproj" />
    <ProjectReference Include="Unity.XR.Interaction.Toolkit.Samples.DeviceSimulator.csproj" />
    <ProjectReference Include="IngameDebugConsole.Runtime.csproj" />
    <ProjectReference Include="Unity.XR.Interaction.Toolkit.Samples.SpatialKeyboard.csproj" />
    <ProjectReference Include="NativeCursor.Examples.csproj" />
    <ProjectReference Include="Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.csproj" />
    <ProjectReference Include="Edgegap.csproj" />
    <ProjectReference Include="Unity.XR.Interaction.Toolkit.Samples.Hands.csproj" />
    <ProjectReference Include="Webview.csproj" />
  </ItemGroup>
  <Import Project="Sdk.targets" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Remove="LaunchProfiles" />
    <ProjectCapability Remove="SharedProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerSharedProjects" />
    <ProjectCapability Remove="ProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerProjects" />
    <ProjectCapability Remove="COMReferences" />
    <ProjectCapability Remove="ReferenceManagerCOM" />
    <ProjectCapability Remove="AssemblyReferences" />
    <ProjectCapability Remove="ReferenceManagerAssemblies" />
  </ItemGroup>
</Project>
